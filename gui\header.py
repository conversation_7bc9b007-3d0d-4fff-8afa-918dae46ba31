"""
Widget header per la finestra principale
"""

from PyQt5.QtWidgets import QFrame, QHBoxLayout, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from models.club import Club

class HeaderWidget(QFrame):
    """Widget header con informazioni del club"""
    
    def __init__(self):
        super().__init__()
        self.current_club = None
        self.init_ui()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumHeight(80)
        
        layout = QHBoxLayout(self)
        
        # Logo del club (placeholder)
        self.club_logo = QLabel("🏆")
        self.club_logo.setFont(QFont("Arial", 24))
        self.club_logo.setAlignment(Qt.AlignCenter)
        self.club_logo.setFixedSize(60, 60)
        layout.addWidget(self.club_logo)
        
        # Informazioni del club
        club_info_layout = QVBoxLayout()
        
        self.club_name_label = QLabel("Nessun Club Selezionato")
        self.club_name_label.setFont(QFont("Arial", 16, QFont.Bold))
        club_info_layout.addWidget(self.club_name_label)
        
        self.club_details_label = QLabel("Seleziona un club per iniziare")
        self.club_details_label.setFont(QFont("Arial", 10))
        club_info_layout.addWidget(self.club_details_label)
        
        layout.addLayout(club_info_layout)
        
        layout.addStretch()
        
        # Informazioni finanziarie
        finance_layout = QVBoxLayout()
        
        self.budget_label = QLabel("Budget: €0")
        self.budget_label.setFont(QFont("Arial", 12, QFont.Bold))
        finance_layout.addWidget(self.budget_label)
        
        self.balance_label = QLabel("Bilancio mensile: €0")
        self.balance_label.setFont(QFont("Arial", 10))
        finance_layout.addWidget(self.balance_label)
        
        layout.addLayout(finance_layout)
    
    def set_club(self, club: Club):
        """Imposta il club corrente"""
        self.current_club = club
        self.update_club_info()
    
    def update_club_info(self):
        """Aggiorna le informazioni del club"""
        if not self.current_club:
            self.club_name_label.setText("Nessun Club Selezionato")
            self.club_details_label.setText("Seleziona un club per iniziare")
            self.budget_label.setText("Budget: €0")
            self.balance_label.setText("Bilancio mensile: €0")
            return
        
        self.club_name_label.setText(self.current_club.nome)
        self.club_details_label.setText(
            f"{self.current_club.citta} - {self.current_club.campionato.value}"
        )
        self.budget_label.setText(f"Budget: €{self.current_club.budget:,}")
        self.balance_label.setText(
            f"Bilancio mensile: €{self.current_club.get_monthly_balance():,}"
        )
