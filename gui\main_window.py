"""
Finestra principale dell'applicazione
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QMenuBar, QStatusBar, QTabWidget, QPushButton,
                            QLabel, QFrame, QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from .club_selection import ClubSelectionDialog
from .dashboard import DashboardWidget
from utils.config import config
from utils.logger import setup_logger

class MainWindow(QMainWindow):
    """Finestra principale del gioco"""
    
    # Segnali
    game_started = pyqtSignal()
    game_paused = pyqtSignal()
    game_resumed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        self.logger = setup_logger("main_window")
        self.current_club = None
        self.current_season = None
        self.game_timer = QTimer()
        self.is_game_running = False
        
        self.init_ui()
        self.setup_connections()
        
        # Mostra la selezione del club all'avvio
        self.show_club_selection()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        self.setWindowTitle("Football President - Gioco Manageriale di Calcio")
        self.setGeometry(100, 100, *config.window_size)
        
        # Widget centrale
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principale
        main_layout = QVBoxLayout(central_widget)
        
        # Header con informazioni del club
        self.header_frame = self.create_header()
        main_layout.addWidget(self.header_frame)
        
        # Tab widget per le diverse sezioni
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Footer con controlli del gioco
        self.footer_frame = self.create_footer()
        main_layout.addWidget(self.footer_frame)
        
        # Menu bar
        self.create_menu_bar()
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Benvenuto in Football President!")
        
        # Inizialmente disabilita tutto
        self.set_game_enabled(False)
    
    def create_header(self) -> QFrame:
        """Crea l'header con le informazioni del club"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumHeight(80)
        
        layout = QHBoxLayout(frame)
        
        # Logo del club (placeholder)
        self.club_logo = QLabel("🏆")
        self.club_logo.setFont(QFont("Arial", 24))
        self.club_logo.setAlignment(Qt.AlignCenter)
        self.club_logo.setFixedSize(60, 60)
        layout.addWidget(self.club_logo)
        
        # Informazioni del club
        club_info_layout = QVBoxLayout()
        
        self.club_name_label = QLabel("Nessun Club Selezionato")
        self.club_name_label.setFont(QFont("Arial", 16, QFont.Bold))
        club_info_layout.addWidget(self.club_name_label)
        
        self.club_details_label = QLabel("Seleziona un club per iniziare")
        self.club_details_label.setFont(QFont("Arial", 10))
        club_info_layout.addWidget(self.club_details_label)
        
        layout.addLayout(club_info_layout)
        
        layout.addStretch()
        
        # Informazioni finanziarie
        finance_layout = QVBoxLayout()
        
        self.budget_label = QLabel("Budget: €0")
        self.budget_label.setFont(QFont("Arial", 12, QFont.Bold))
        finance_layout.addWidget(self.budget_label)
        
        self.balance_label = QLabel("Bilancio mensile: €0")
        self.balance_label.setFont(QFont("Arial", 10))
        finance_layout.addWidget(self.balance_label)
        
        layout.addLayout(finance_layout)
        
        return frame
    
    def create_footer(self) -> QFrame:
        """Crea il footer con i controlli del gioco"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMaximumHeight(60)
        
        layout = QHBoxLayout(frame)
        
        # Data corrente
        self.date_label = QLabel("Data: --/--/----")
        self.date_label.setFont(QFont("Arial", 12))
        layout.addWidget(self.date_label)
        
        layout.addStretch()
        
        # Controlli del gioco
        self.play_pause_btn = QPushButton("▶ Avvia")
        self.play_pause_btn.setFixedSize(100, 40)
        self.play_pause_btn.clicked.connect(self.toggle_game)
        layout.addWidget(self.play_pause_btn)
        
        self.advance_week_btn = QPushButton("Avanza Settimana")
        self.advance_week_btn.setFixedSize(120, 40)
        self.advance_week_btn.clicked.connect(self.advance_week)
        layout.addWidget(self.advance_week_btn)
        
        # Velocità di simulazione
        speed_layout = QVBoxLayout()
        speed_label = QLabel("Velocità:")
        speed_label.setFont(QFont("Arial", 8))
        speed_layout.addWidget(speed_label)
        
        self.speed_label = QLabel("1x")
        self.speed_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.speed_label.setAlignment(Qt.AlignCenter)
        speed_layout.addWidget(self.speed_label)
        
        layout.addLayout(speed_layout)
        
        return frame
    
    def create_menu_bar(self):
        """Crea la barra dei menu"""
        menubar = self.menuBar()
        
        # Menu File
        file_menu = menubar.addMenu('File')
        
        new_game_action = file_menu.addAction('Nuova Partita')
        new_game_action.triggered.connect(self.new_game)
        
        load_game_action = file_menu.addAction('Carica Partita')
        load_game_action.triggered.connect(self.load_game)
        
        save_game_action = file_menu.addAction('Salva Partita')
        save_game_action.triggered.connect(self.save_game)
        
        file_menu.addSeparator()
        
        exit_action = file_menu.addAction('Esci')
        exit_action.triggered.connect(self.close)
        
        # Menu Visualizza
        view_menu = menubar.addMenu('Visualizza')
        
        fullscreen_action = view_menu.addAction('Schermo Intero')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        
        # Menu Aiuto
        help_menu = menubar.addMenu('Aiuto')
        
        about_action = help_menu.addAction('Informazioni')
        about_action.triggered.connect(self.show_about)
    
    def setup_connections(self):
        """Configura le connessioni dei segnali"""
        self.game_timer.timeout.connect(self.game_tick)
        
    def show_club_selection(self):
        """Mostra la finestra di selezione del club"""
        dialog = ClubSelectionDialog(self)
        if dialog.exec_() == dialog.Accepted:
            selected_club = dialog.get_selected_club()
            if selected_club:
                self.start_new_game(selected_club)
    
    def start_new_game(self, club):
        """Inizia una nuova partita con il club selezionato"""
        self.current_club = club
        self.logger.info(f"Iniziata nuova partita con {club.nome}")
        
        # Aggiorna l'interfaccia
        self.update_club_info()
        self.setup_game_tabs()
        self.set_game_enabled(True)
        
        self.status_bar.showMessage(f"Partita iniziata con {club.nome}")
    
    def update_club_info(self):
        """Aggiorna le informazioni del club nell'header"""
        if not self.current_club:
            return
        
        self.club_name_label.setText(self.current_club.nome)
        self.club_details_label.setText(
            f"{self.current_club.citta} - {self.current_club.campionato.value}"
        )
        self.budget_label.setText(f"Budget: €{self.current_club.budget:,}")
        self.balance_label.setText(
            f"Bilancio mensile: €{self.current_club.get_monthly_balance():,}"
        )
    
    def setup_game_tabs(self):
        """Configura i tab del gioco"""
        # Rimuovi tab esistenti
        self.tab_widget.clear()
        
        if not self.current_club:
            return
        
        # Tab Dashboard
        self.dashboard = DashboardWidget(self.current_club)
        self.tab_widget.addTab(self.dashboard, "Dashboard")
        
        # Altri tab verranno aggiunti qui
        # self.tab_widget.addTab(StaffWidget(self.current_club), "Staff")
        # self.tab_widget.addTab(PlayersWidget(self.current_club), "Giocatori")
        # self.tab_widget.addTab(FinancesWidget(self.current_club), "Finanze")
        # self.tab_widget.addTab(MarketWidget(self.current_club), "Mercato")
        # self.tab_widget.addTab(FixturesWidget(self.current_club), "Calendario")
    
    def set_game_enabled(self, enabled: bool):
        """Abilita/disabilita i controlli del gioco"""
        self.tab_widget.setEnabled(enabled)
        self.play_pause_btn.setEnabled(enabled)
        self.advance_week_btn.setEnabled(enabled)
    
    def toggle_game(self):
        """Avvia/mette in pausa il gioco"""
        if self.is_game_running:
            self.pause_game()
        else:
            self.start_game()
    
    def start_game(self):
        """Avvia la simulazione del gioco"""
        self.is_game_running = True
        self.play_pause_btn.setText("⏸ Pausa")
        self.game_timer.start(1000)  # 1 secondo
        self.game_started.emit()
        self.logger.info("Gioco avviato")
    
    def pause_game(self):
        """Mette in pausa la simulazione del gioco"""
        self.is_game_running = False
        self.play_pause_btn.setText("▶ Riprendi")
        self.game_timer.stop()
        self.game_paused.emit()
        self.logger.info("Gioco in pausa")
    
    def advance_week(self):
        """Avanza di una settimana manualmente"""
        if self.current_season:
            self.current_season.advance_week()
            self.update_game_state()
            self.logger.info("Avanzata di una settimana")
    
    def game_tick(self):
        """Tick del gioco (chiamato dal timer)"""
        # Qui implementerai la logica di avanzamento automatico
        pass
    
    def update_game_state(self):
        """Aggiorna lo stato del gioco nell'interfaccia"""
        self.update_club_info()
        
        if hasattr(self, 'dashboard'):
            self.dashboard.update_data()
    
    def new_game(self):
        """Inizia una nuova partita"""
        reply = QMessageBox.question(
            self, 'Nuova Partita',
            'Sei sicuro di voler iniziare una nuova partita?\n'
            'I progressi attuali andranno persi.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.show_club_selection()
    
    def load_game(self):
        """Carica una partita salvata"""
        QMessageBox.information(self, "Carica Partita", "Funzione non ancora implementata")
    
    def save_game(self):
        """Salva la partita corrente"""
        QMessageBox.information(self, "Salva Partita", "Funzione non ancora implementata")
    
    def toggle_fullscreen(self):
        """Attiva/disattiva la modalità schermo intero"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def show_about(self):
        """Mostra le informazioni sull'applicazione"""
        QMessageBox.about(
            self, "Informazioni",
            "Football President v1.0\n\n"
            "Un gioco manageriale di calcio dove interpreti\n"
            "il presidente di un club italiano.\n\n"
            "Sviluppato con Python e PyQt5"
        )
    
    def closeEvent(self, event):
        """Gestisce la chiusura dell'applicazione"""
        reply = QMessageBox.question(
            self, 'Esci',
            'Sei sicuro di voler uscire dal gioco?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Salva configurazione
            config.save()
            event.accept()
        else:
            event.ignore()
