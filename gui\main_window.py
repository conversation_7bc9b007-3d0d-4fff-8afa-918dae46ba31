"""
Finestra principale dell'applicazione
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QMenuBar, QStatusBar, QTabWidget, QPushButton,
                            QLabel, QFrame, QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from .club_selection import ClubSelectionDialog
from .dashboard import DashboardWidget
from .header import HeaderWidget
from .footer import FooterWidget
from .menu_manager import MenuManager
from .finances import FinancesWidget
from .squad import SquadWidget
from utils.config import config
from utils.logger import setup_logger

class MainWindow(QMainWindow):
    """Finestra principale del gioco"""
    
    # Segnali
    game_started = pyqtSignal()
    game_paused = pyqtSignal()
    game_resumed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        self.logger = setup_logger("main_window")
        self.current_club = None
        self.current_season = None
        self.game_timer = QTimer()
        self.is_game_running = False
        
        self.init_ui()
        self.setup_connections()
        
        # Mostra la selezione del club all'avvio
        self.show_club_selection()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        self.setWindowTitle("Football President - Gioco Manageriale di Calcio")
        self.setGeometry(100, 100, *config.window_size)
        
        # Widget centrale
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principale
        main_layout = QVBoxLayout(central_widget)
        
        # Header con informazioni del club
        self.header = HeaderWidget()
        main_layout.addWidget(self.header)
        
        # Tab widget per le diverse sezioni
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Footer con controlli del gioco
        self.footer = FooterWidget()
        main_layout.addWidget(self.footer)
        
        # Menu bar
        self.menu_manager = MenuManager(self)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Benvenuto in Football President!")
        
        # Inizialmente disabilita tutto
        self.set_game_enabled(False)
    

    

    

    
    def setup_connections(self):
        """Configura le connessioni dei segnali"""
        self.game_timer.timeout.connect(self.game_tick)

        # Connessioni footer
        self.footer.game_toggled.connect(self.on_game_toggled)
        self.footer.week_advanced.connect(self.on_week_advanced)
        self.footer.event_advanced.connect(self.on_event_advanced)

        # Connessioni menu
        self.menu_manager.new_game_requested.connect(self.show_club_selection)
        
    def show_club_selection(self):
        """Mostra la finestra di selezione del club"""
        dialog = ClubSelectionDialog(self)
        if dialog.exec_() == dialog.Accepted:
            selected_club = dialog.get_selected_club()
            if selected_club:
                self.start_new_game(selected_club)
    
    def start_new_game(self, club):
        """Inizia una nuova partita con il club selezionato"""
        self.current_club = club
        self.logger.info(f"Iniziata nuova partita con {club.nome}")

        # Aggiorna l'interfaccia
        self.header.set_club(club)
        self.setup_game_tabs()
        self.footer.set_game_enabled(True)

        self.status_bar.showMessage(f"Partita iniziata con {club.nome}")

    def on_game_toggled(self, is_running: bool):
        """Gestisce l'avvio/pausa del gioco"""
        self.is_game_running = is_running

        if is_running:
            self.game_timer.start(1000)  # 1 secondo
            self.game_started.emit()
            self.logger.info("Gioco avviato")
        else:
            self.game_timer.stop()
            self.game_paused.emit()
            self.logger.info("Gioco in pausa")

    def on_week_advanced(self):
        """Gestisce l'avanzamento di una settimana"""
        self.update_game_state()
        self.footer.check_calendar_events()
        self.logger.info(f"Avanzata di una settimana - Nuova data: {self.footer.date_label.text()}")

    def on_event_advanced(self):
        """Gestisce l'avanzamento al prossimo evento"""
        self.update_game_state()
        self.footer.check_calendar_events()
        self.logger.info("Avanzato al prossimo evento")
    
    def update_club_info(self):
        """Aggiorna le informazioni del club nell'header"""
        if not self.current_club:
            return
        
        self.club_name_label.setText(self.current_club.nome)
        self.club_details_label.setText(
            f"{self.current_club.citta} - {self.current_club.campionato.value}"
        )
        self.budget_label.setText(f"Budget: €{self.current_club.budget:,}")
        self.balance_label.setText(
            f"Bilancio mensile: €{self.current_club.get_monthly_balance():,}"
        )
    
    def setup_game_tabs(self):
        """Configura i tab del gioco"""
        # Rimuovi tab esistenti
        self.tab_widget.clear()

        if not self.current_club:
            return

        # Tab Dashboard
        self.dashboard = DashboardWidget(self.current_club)
        self.tab_widget.addTab(self.dashboard, "📊 Dashboard")

        # Tab Rosa
        self.squad = SquadWidget(self.current_club)
        self.tab_widget.addTab(self.squad, "👥 Rosa")

        # Tab Finanze
        self.finances = FinancesWidget(self.current_club)
        self.tab_widget.addTab(self.finances, "💰 Finanze")

        # Connessione per aggiornare il tab corrente quando cambia la selezione
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # Altri tab da implementare
        # self.tab_widget.addTab(StaffWidget(self.current_club), "👔 Staff")
        # self.tab_widget.addTab(MarketWidget(self.current_club), "🔄 Mercato")
        # self.tab_widget.addTab(FixturesWidget(self.current_club), "📅 Calendario")
        # self.tab_widget.addTab(SettingsWidget(), "⚙️ Impostazioni")
    
    def set_game_enabled(self, enabled: bool):
        """Abilita/disabilita i controlli del gioco"""
        self.tab_widget.setEnabled(enabled)
        self.footer.set_game_enabled(enabled)
    

    
    def game_tick(self):
        """Tick del gioco (chiamato dal timer)"""
        # Qui implementerai la logica di avanzamento automatico
        pass
    
    def update_game_state(self):
        """Aggiorna lo stato del gioco nell'interfaccia"""
        if self.current_club:
            self.header.update_club_info()

        self.footer.update_calendar_info()

        # Aggiorna tutti i widget dei tab
        if hasattr(self, 'dashboard'):
            self.dashboard.update_data()

        if hasattr(self, 'squad'):
            self.squad.update_data()

        if hasattr(self, 'finances'):
            self.finances.update_data()


    

    
    def on_tab_changed(self, index: int):
        """Gestisce il cambio di tab e aggiorna il widget attivo"""
        current_widget = self.tab_widget.widget(index)
        if hasattr(current_widget, 'update_data'):
            current_widget.update_data()

    def closeEvent(self, event):
        """Gestisce la chiusura dell'applicazione"""
        reply = QMessageBox.question(
            self, 'Esci',
            'Sei sicuro di voler uscire dal gioco?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Salva configurazione
            config.save()
            event.accept()
        else:
            event.ignore()
