"""
Generatore di giocatori per popolare le rose delle squadre
"""

import random
from typing import List, Dict, Optional
from models.player import Player, Position
from models.club import Club, League
from utils.name_generator import name_generator

class PlayerGenerator:
    """Classe per generare giocatori realistici"""
    
    def __init__(self):
        self.player_id_counter = 1
    
    def generate_squad(self, club: Club, squad_size: int = 25) -> List[Player]:
        """Genera una rosa completa per un club"""
        players = []

        # Distribuzione posizioni per una rosa tipica
        position_distribution = {
            Position.PORTIERE: 3,
            Position.DIFENSORE: 8,
            Position.CENTROCAMPISTA: 8,
            Position.ATTACCANTE: 6
        }

        # Aggiusta la distribuzione se necessario
        total_positions = sum(position_distribution.values())
        if total_positions != squad_size:
            # Aggiusta i centrocampisti
            diff = squad_size - total_positions
            position_distribution[Position.CENTROCAMPISTA] += diff

        # Determina bias nazionalità basato sul campionato
        if club.campionato == League.EUROPA:
            nationality_bias = 0.3  # 30% nazionalità locale, 70% internazionale
        else:
            nationality_bias = 0.7  # 70% italiani per squadre italiane

        # Genera giocatori per ogni posizione
        for position, count in position_distribution.items():
            for _ in range(count):
                player = self.generate_player(
                    position=position,
                    club_level=self._get_club_level(club),
                    nationality_bias=nationality_bias,
                    club=club  # Passa il club per determinare nazionalità
                )
                players.append(player)

        return players
    
    def generate_player(self,
                       position: Position,
                       club_level: str = "medium",
                       nationality_bias: float = 0.7,
                       age_range: tuple = (18, 35),
                       club: Club = None) -> Player:
        """Genera un singolo giocatore"""
        
        # Determina nazionalità
        if club and club.campionato == League.EUROPA:
            nationality = self._get_european_nationality(club, nationality_bias)
        else:
            # Squadre italiane
            if random.random() < nationality_bias:
                nationality = "Italia"
            else:
                nationality = name_generator.get_random_nationality()
        
        # Genera nome
        nome, cognome = name_generator.generate_name_by_nationality(nationality)
        
        # Genera età
        eta = self._generate_age_by_position(position, age_range)
        
        # Calcola valore di mercato e stipendio basati su livello del club
        valore_mercato, stipendio = self._calculate_player_value(club_level, position, eta)
        
        # Genera contratto
        contratto_scadenza = random.randint(2025, 2028)
        
        player = Player(
            player_id=self.player_id_counter,
            nome=nome,
            cognome=cognome,
            eta=eta,
            posizione=position,
            nazionalita=nationality,
            valore_mercato=valore_mercato,
            stipendio=stipendio,
            contratto_scadenza=contratto_scadenza
        )
        
        self.player_id_counter += 1
        return player

    def _get_european_nationality(self, club: Club, nationality_bias: float) -> str:
        """Determina la nazionalità per squadre europee"""
        # Mappa squadre europee alle loro nazionalità principali
        club_nationalities = {
            # Spagna
            "Real Madrid": "Spagna", "FC Barcelona": "Spagna", "Atlético Madrid": "Spagna",
            "Real Betis": "Spagna", "Athletic Club": "Spagna", "Valencia CF": "Spagna",
            "Girona FC": "Spagna", "CA Osasuna": "Spagna", "Celta Vigo": "Spagna",
            "Real Sociedad": "Spagna", "Villarreal CF": "Spagna", "Sevilla FC": "Spagna",

            # Inghilterra
            "Manchester City": "Inghilterra", "Liverpool FC": "Inghilterra", "Arsenal FC": "Inghilterra",
            "Manchester United": "Inghilterra", "Tottenham Hotspur": "Inghilterra", "Chelsea FC": "Inghilterra",
            "West Ham United": "Inghilterra", "Brighton & Hove Albion": "Inghilterra", "Brentford FC": "Inghilterra",
            "Crystal Palace": "Inghilterra", "Fulham FC": "Inghilterra", "Wolverhampton Wanderers": "Inghilterra",
            "AFC Bournemouth": "Inghilterra", "Aston Villa": "Inghilterra", "Newcastle United": "Inghilterra",

            # Germania
            "Bayern München": "Germania", "Borussia Dortmund": "Germania", "Bayer 04 Leverkusen": "Germania",
            "RB Leipzig": "Germania", "Eintracht Frankfurt": "Germania", "SC Freiburg": "Germania",
            "Union Berlin": "Germania", "Borussia Mönchengladbach": "Germania", "VfL Wolfsburg": "Germania",
            "TSG Hoffenheim": "Germania", "VfB Stuttgart": "Germania", "VfL Bochum": "Germania",
            "FC Heidenheim": "Germania",

            # Francia
            "Paris Saint-Germain": "Francia", "Olympique Lyonnais": "Francia", "Lille OSC": "Francia",
            "Olympique de Marseille": "Francia", "Stade Rennais FC": "Francia", "OGC Nice": "Francia",
            "RC Lens": "Francia", "Stade de Reims": "Francia", "Montpellier HSC": "Francia",
            "RC Strasbourg Alsace": "Francia",

            # Portogallo
            "FC Porto": "Portogallo", "SL Benfica": "Portogallo", "Sporting CP": "Portogallo",
            "SC Braga": "Portogallo", "Vitória SC (Guimarães)": "Portogallo",

            # Olanda
            "Ajax": "Olanda", "PSV Eindhoven": "Olanda", "Feyenoord": "Olanda",
            "AZ Alkmaar": "Olanda", "FC Twente": "Olanda",

            # Altri paesi
            "Celtic FC": "Scozia", "Rangers FC": "Scozia",
            "Galatasaray SK": "Turchia", "Fenerbahçe SK": "Turchia", "Beşiktaş JK": "Turchia",
            "İstanbul Başakşehir": "Turchia", "Trabzonspor": "Turchia"
        }

        # Determina nazionalità locale del club
        local_nationality = club_nationalities.get(club.nome, "Europa")

        if random.random() < nationality_bias:
            return local_nationality
        else:
            # Mix internazionale per squadre europee
            international_nationalities = [
                "Brasile", "Argentina", "Francia", "Spagna", "Germania", "Inghilterra",
                "Portogallo", "Olanda", "Belgio", "Croazia", "Serbia", "Polonia",
                "Danimarca", "Svezia", "Norvegia", "Svizzera", "Austria", "Repubblica Ceca"
            ]
            return random.choice(international_nationalities)
    
    def _get_club_level(self, club: Club) -> str:
        """Determina il livello del club"""
        if club.campionato == League.EUROPA:
            # Squadre europee - livello basato sul budget
            if club.budget > 500000000:  # 500M+
                return "world_class"
            elif club.budget > 200000000:  # 200M+
                return "elite"
            elif club.budget > 100000000:  # 100M+
                return "top"
            elif club.budget > 50000000:   # 50M+
                return "medium_high"
            else:
                return "medium"
        elif club.campionato == League.SERIE_A:
            # Distingui tra top, medium e low in Serie A
            if club.budget > 50000000:
                return "top"
            elif club.budget > 20000000:
                return "medium_high"
            else:
                return "medium"
        elif club.campionato == League.SERIE_B:
            return "medium_low"
        else:  # Serie C
            return "low"
    
    def _generate_age_by_position(self, position: Position, age_range: tuple) -> int:
        """Genera età basata sulla posizione"""
        min_age, max_age = age_range
        
        if position == Position.PORTIERE:
            # I portieri tendono ad essere più maturi
            weights = self._create_age_weights(min_age, max_age, peak_age=28)
        elif position == Position.DIFENSORE:
            # I difensori maturano presto
            weights = self._create_age_weights(min_age, max_age, peak_age=26)
        elif position == Position.CENTROCAMPISTA:
            # I centrocampisti hanno un range ampio
            weights = self._create_age_weights(min_age, max_age, peak_age=25)
        else:  # ATTACCANTE
            # Gli attaccanti possono essere giovani talenti
            weights = self._create_age_weights(min_age, max_age, peak_age=24)
        
        ages = list(range(min_age, max_age + 1))
        return random.choices(ages, weights=weights)[0]
    
    def _create_age_weights(self, min_age: int, max_age: int, peak_age: int) -> List[float]:
        """Crea pesi per la distribuzione dell'età"""
        ages = list(range(min_age, max_age + 1))
        weights = []
        
        for age in ages:
            if age < 20:
                weight = 0.5  # Pochi giovanissimi
            elif age < 23:
                weight = 1.5  # Giovani promettenti
            elif age <= peak_age:
                weight = 2.0  # Età di picco
            elif age <= peak_age + 3:
                weight = 1.8  # Ancora nel pieno
            elif age <= 30:
                weight = 1.2  # Esperti
            elif age <= 33:
                weight = 0.8  # Veterani
            else:
                weight = 0.3  # Molto anziani
            
            weights.append(weight)
        
        return weights
    
    def _calculate_player_value(self, club_level: str, position: Position, age: int) -> tuple:
        """Calcola valore di mercato e stipendio"""

        # Valori base per livello del club
        base_values = {
            "world_class": (80000000, 500000),    # Real Madrid, Man City, PSG
            "elite": (40000000, 300000),          # Liverpool, Arsenal, Bayern
            "top": (15000000, 150000),            # Top clubs europei
            "medium_high": (5000000, 80000),      # Squadre europee medie
            "medium": (2000000, 40000),           # Serie A / squadre minori Europa
            "medium_low": (800000, 20000),        # Serie B
            "low": (200000, 8000)                 # Serie C
        }
        
        base_value, base_salary = base_values.get(club_level, (100000, 5000))
        
        # Modificatori per posizione
        position_multipliers = {
            Position.PORTIERE: 0.8,
            Position.DIFENSORE: 0.9,
            Position.CENTROCAMPISTA: 1.0,
            Position.ATTACCANTE: 1.2
        }
        
        position_mult = position_multipliers.get(position, 1.0)
        
        # Modificatori per età
        if age < 20:
            age_mult = 0.6
        elif age < 23:
            age_mult = 0.8
        elif age < 28:
            age_mult = 1.0
        elif age < 32:
            age_mult = 0.8
        else:
            age_mult = 0.5
        
        # Variazione casuale
        random_mult = random.uniform(0.7, 1.3)
        
        final_value = int(base_value * position_mult * age_mult * random_mult)
        final_salary = int(base_salary * position_mult * age_mult * random_mult)
        
        return final_value, final_salary
    
    def generate_free_agents(self, count: int = 50) -> List[Player]:
        """Genera una lista di giocatori svincolati"""
        free_agents = []
        
        for _ in range(count):
            position = random.choice(list(Position))
            
            # I giocatori svincolati tendono ad essere più anziani o giovani
            if random.random() < 0.6:
                age_range = (28, 35)  # Veterani
            else:
                age_range = (18, 22)  # Giovani
            
            player = self.generate_player(
                position=position,
                club_level="medium_low",  # Livello medio-basso per svincolati
                nationality_bias=0.5,  # Mix di nazionalità
                age_range=age_range
            )
            
            # Riduci valore e stipendio per svincolati
            player.valore_mercato = int(player.valore_mercato * 0.7)
            player.stipendio = int(player.stipendio * 0.8)
            
            free_agents.append(player)
        
        return free_agents
    
    def generate_youth_player(self, club: Club) -> Player:
        """Genera un giocatore del settore giovanile"""
        position = random.choice(list(Position))
        
        player = self.generate_player(
            position=position,
            club_level="low",  # I giovani partono con valori bassi
            nationality_bias=0.9,  # Principalmente italiani nel settore giovanile
            age_range=(16, 19)
        )
        
        # I giovani hanno potenziale di crescita
        player.valore_mercato = int(player.valore_mercato * 0.3)
        player.stipendio = int(player.stipendio * 0.2)
        
        # Bonus potenziale per i giovani
        growth_potential = random.randint(5, 20)
        for stat_name in ['tecnica', 'velocita', 'resistenza', 'forza', 'mentalita']:
            current_value = getattr(player.stats, stat_name)
            setattr(player.stats, stat_name, min(95, current_value + growth_potential))
        
        return player
    
    def update_player_values_seasonally(self, players: List[Player]):
        """Aggiorna i valori dei giocatori a fine stagione"""
        for player in players:
            # Fattori che influenzano il valore
            age_factor = self._get_age_value_factor(player.eta)
            performance_factor = self._get_performance_factor(player)
            form_factor = (player.forma + player.morale) / 200
            
            # Calcola nuovo valore
            value_multiplier = age_factor * performance_factor * form_factor
            new_value = int(player.valore_mercato * value_multiplier)
            
            # Limita le variazioni eccessive
            max_change = player.valore_mercato * 0.5
            if abs(new_value - player.valore_mercato) > max_change:
                if new_value > player.valore_mercato:
                    new_value = player.valore_mercato + int(max_change)
                else:
                    new_value = player.valore_mercato - int(max_change)
            
            player.valore_mercato = max(10000, new_value)  # Valore minimo
    
    def _get_age_value_factor(self, age: int) -> float:
        """Fattore di valore basato sull'età"""
        if age < 20:
            return 1.1  # I giovani crescono
        elif age < 28:
            return 1.0  # Età stabile
        elif age < 32:
            return 0.95  # Leggero declino
        else:
            return 0.9  # Declino più marcato
    
    def _get_performance_factor(self, player: Player) -> float:
        """Fattore di valore basato sulle performance"""
        if player.presenze == 0:
            return 1.0  # Nessuna variazione se non ha giocato
        
        # Calcola un punteggio performance
        goals_per_game = player.gol / player.presenze if player.presenze > 0 else 0
        assists_per_game = player.assist / player.presenze if player.presenze > 0 else 0
        
        performance_score = 0
        
        if player.posizione == Position.ATTACCANTE:
            performance_score = goals_per_game * 2 + assists_per_game
        elif player.posizione == Position.CENTROCAMPISTA:
            performance_score = goals_per_game + assists_per_game * 1.5
        elif player.posizione == Position.DIFENSORE:
            performance_score = goals_per_game * 3 + assists_per_game  # I gol dei difensori valgono di più
        else:  # PORTIERE
            # Per i portieri, meno gol subiti = meglio (logica semplificata)
            performance_score = max(0, 1 - (player.cartellini_rossi * 0.1))
        
        # Converti in fattore moltiplicativo
        if performance_score > 0.5:
            return 1.2  # Ottime performance
        elif performance_score > 0.3:
            return 1.1  # Buone performance
        elif performance_score > 0.1:
            return 1.0  # Performance normali
        else:
            return 0.9  # Performance scarse

# Istanza globale del generatore
player_generator = PlayerGenerator()
