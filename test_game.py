#!/usr/bin/env python3
"""
Script di test per verificare il funzionamento del gioco
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.club import Club, League
from models.player import Position
from core.player_generator import player_generator
from utils.json_loader import data_loader
from utils.name_generator import name_generator

def test_data_loading():
    """Test caricamento dati"""
    print("🔄 Test caricamento dati...")
    
    # Test caricamento squadre Serie A
    serie_a_data = data_loader.load_csv("serie_a.csv")
    if serie_a_data:
        print(f"✅ Serie A: {len(serie_a_data)} squadre caricate")
        print(f"   Prima squadra: {serie_a_data[0].get('squadra', 'N/A')}")
    else:
        print("❌ Errore nel caricamento Serie A")
    
    # Test caricamento nomi
    names_data = data_loader.load_json("player_names.json")
    if names_data:
        print("✅ Nomi giocatori caricati")
    else:
        print("❌ Errore nel caricamento nomi")

def test_club_creation():
    """Test creazione club"""
    print("\n🔄 Test creazione club...")
    
    club = Club(
        club_id=1,
        nome="Test FC",
        citta="Test City",
        campionato=League.SERIE_A,
        budget=50000000,
        stadio_nome="Test Stadium",
        stadio_capacita=30000
    )
    
    print(f"✅ Club creato: {club}")
    print(f"   Budget: €{club.budget:,}")
    print(f"   Stadio: {club.stadio_nome} ({club.stadio_capacita:,} posti)")

def test_player_generation():
    """Test generazione giocatori"""
    print("\n🔄 Test generazione giocatori...")
    
    # Crea un club di test
    club = Club(
        club_id=1,
        nome="Test FC",
        citta="Test City",
        campionato=League.SERIE_B,
        budget=10000000
    )
    
    # Genera alcuni giocatori
    players = []
    for position in Position:
        player = player_generator.generate_player(position, "medium")
        players.append(player)
        club.add_player(player)
    
    print(f"✅ Generati {len(players)} giocatori")
    for player in players:
        print(f"   {player}")
    
    print(f"✅ Overall medio squadra: {club.get_team_overall()}")

def test_name_generation():
    """Test generazione nomi"""
    print("\n🔄 Test generazione nomi...")
    
    # Genera nomi italiani
    for i in range(3):
        nome, cognome = name_generator.generate_italian_name()
        print(f"   Italiano: {nome} {cognome}")
    
    # Genera nomi stranieri
    for i in range(3):
        nome, cognome = name_generator.generate_foreign_name()
        print(f"   Straniero: {nome} {cognome}")
    
    print("✅ Generazione nomi funzionante")

def test_squad_generation():
    """Test generazione rosa completa"""
    print("\n🔄 Test generazione rosa completa...")
    
    club = Club(
        club_id=1,
        nome="Test United",
        citta="Test Town",
        campionato=League.SERIE_C,
        budget=5000000
    )
    
    # Genera rosa completa
    squad = player_generator.generate_squad(club)
    
    for player in squad:
        club.add_player(player)
    
    print(f"✅ Rosa generata: {len(squad)} giocatori")
    
    # Statistiche per posizione
    for position in Position:
        players_in_position = club.get_players_by_position(position)
        count = len(players_in_position)
        avg_overall = int(sum(p.stats.overall() for p in players_in_position) / count) if count > 0 else 0
        print(f"   {position.value}: {count} giocatori (Overall medio: {avg_overall})")
    
    print(f"✅ Overall squadra: {club.get_team_overall()}")
    print(f"✅ Stipendi totali: €{sum(p.stipendio for p in club.giocatori):,}/mese")

def main():
    """Esegue tutti i test"""
    print("🚀 Football President - Test Suite")
    print("=" * 50)
    
    try:
        test_data_loading()
        test_club_creation()
        test_name_generation()
        test_player_generation()
        test_squad_generation()
        
        print("\n" + "=" * 50)
        print("✅ Tutti i test completati con successo!")
        print("🎮 Il gioco è pronto per essere utilizzato!")
        
    except Exception as e:
        print(f"\n❌ Errore durante i test: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
