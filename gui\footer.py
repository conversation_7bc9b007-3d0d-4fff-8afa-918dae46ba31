"""
Widget footer con controlli del gioco e calendario
"""

from PyQt5.QtWidgets import (QFrame, QHBoxLayout, QVBoxLayout, QLabel, 
                            QPushButton, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from core.game_calendar import game_calendar

class FooterWidget(QFrame):
    """Widget footer con controlli del gioco"""
    
    # Segnali per comunicare con la finestra principale
    game_toggled = pyqtSignal(bool)  # True = avvia, False = pausa
    week_advanced = pyqtSignal()
    event_advanced = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.is_game_running = False
        self.init_ui()
        self.update_calendar_info()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMaximumHeight(60)
        
        layout = QHBoxLayout(self)
        
        # Data corrente e fase stagione
        self.date_label = QLabel()
        self.date_label.setFont(QFont("Arial", 12))
        layout.addWidget(self.date_label)
        
        # Fase della stagione
        self.phase_label = QLabel()
        self.phase_label.setFont(QFont("Arial", 10))
        self.phase_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.phase_label)
        
        layout.addStretch()
        
        # Controlli del gioco
        self.play_pause_btn = QPushButton("▶ Avvia")
        self.play_pause_btn.setFixedSize(100, 40)
        self.play_pause_btn.clicked.connect(self.toggle_game)
        layout.addWidget(self.play_pause_btn)
        
        self.advance_week_btn = QPushButton("Avanza Settimana")
        self.advance_week_btn.setFixedSize(120, 40)
        self.advance_week_btn.clicked.connect(self.advance_week)
        layout.addWidget(self.advance_week_btn)
        
        self.advance_event_btn = QPushButton("Prossimo Evento")
        self.advance_event_btn.setFixedSize(120, 40)
        self.advance_event_btn.clicked.connect(self.advance_to_next_event)
        layout.addWidget(self.advance_event_btn)
        
        # Velocità di simulazione
        speed_layout = QVBoxLayout()
        speed_label = QLabel("Velocità:")
        speed_label.setFont(QFont("Arial", 8))
        speed_layout.addWidget(speed_label)
        
        self.speed_label = QLabel("1x")
        self.speed_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.speed_label.setAlignment(Qt.AlignCenter)
        speed_layout.addWidget(self.speed_label)
        
        layout.addLayout(speed_layout)
    
    def set_game_enabled(self, enabled: bool):
        """Abilita/disabilita i controlli del gioco"""
        self.play_pause_btn.setEnabled(enabled)
        self.advance_week_btn.setEnabled(enabled)
        self.advance_event_btn.setEnabled(enabled)
    
    def toggle_game(self):
        """Avvia/mette in pausa il gioco"""
        self.is_game_running = not self.is_game_running
        
        if self.is_game_running:
            self.play_pause_btn.setText("⏸ Pausa")
        else:
            self.play_pause_btn.setText("▶ Riprendi")
        
        self.game_toggled.emit(self.is_game_running)
    
    def advance_week(self):
        """Avanza di una settimana manualmente"""
        game_calendar.advance_days(7)
        self.update_calendar_info()
        self.week_advanced.emit()
    
    def advance_to_next_event(self):
        """Avanza direttamente al prossimo evento importante"""
        phase_info = game_calendar.get_phase_info()
        days_until = game_calendar.get_days_until_next_event()
        
        if days_until > 0:
            reply = QMessageBox.question(
                self, 
                "Avanza al Prossimo Evento",
                f"Vuoi avanzare di {days_until} giorni fino a:\n"
                f"{phase_info['prossimo_evento']} ({phase_info['data_prossimo_evento']})?\n\n"
                f"Questo salterà il tempo intermedio.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                game_calendar.advance_to_next_event()
                self.update_calendar_info()
                self.event_advanced.emit()
        else:
            QMessageBox.information(
                self, 
                "Nessun Evento",
                "Non ci sono eventi programmati nel prossimo futuro."
            )
    
    def update_calendar_info(self):
        """Aggiorna le informazioni del calendario"""
        # Data corrente
        current_date = game_calendar.get_current_date_string()
        season_info = game_calendar.get_season_info()
        self.date_label.setText(f"📅 {current_date} - Stagione {season_info['stagione']}")
        
        # Fase della stagione
        phase_info = game_calendar.get_phase_info()
        days_until = game_calendar.get_days_until_next_event()
        
        phase_text = f"🎯 {phase_info['fase']}"
        if days_until > 0:
            phase_text += f" - {days_until} giorni a {phase_info['prossimo_evento']}"
        
        self.phase_label.setText(phase_text)
        
        # Colore della fase
        phase = game_calendar.get_current_phase()
        if phase.value == "Calciomercato Estivo" or phase.value == "Calciomercato Invernale":
            self.phase_label.setStyleSheet("color: #FF9800; font-style: italic; font-weight: bold;")
        elif "Campionato" in phase.value:
            self.phase_label.setStyleSheet("color: #4CAF50; font-style: italic; font-weight: bold;")
        elif "Playoff" in phase.value:
            self.phase_label.setStyleSheet("color: #F44336; font-style: italic; font-weight: bold;")
        else:
            self.phase_label.setStyleSheet("color: #666; font-style: italic;")
    
    def check_calendar_events(self):
        """Controlla e notifica eventi importanti del calendario"""
        phase_info = game_calendar.get_phase_info()
        days_until = game_calendar.get_days_until_next_event()
        
        # Notifiche per eventi imminenti
        if days_until <= 7 and days_until > 0:
            event_name = phase_info['prossimo_evento']
            QMessageBox.information(
                self, 
                "Evento Imminente",
                f"⚠️ Attenzione!\n\n"
                f"{event_name} tra {days_until} giorni.\n"
                f"Data: {phase_info['data_prossimo_evento']}\n\n"
                f"Preparati per questo importante evento!"
            )
        
        # Notifiche per cambio fase
        current_phase = game_calendar.get_current_phase()
        if hasattr(self, '_last_phase') and self._last_phase != current_phase:
            self.notify_phase_change(current_phase)
        
        self._last_phase = current_phase
    
    def notify_phase_change(self, new_phase):
        """Notifica il cambio di fase della stagione"""
        phase_messages = {
            "Calciomercato Estivo": {
                "title": "🔄 Calciomercato Estivo Aperto!",
                "message": "Il calciomercato estivo è ora aperto!\n\n"
                          "Puoi acquistare e vendere giocatori.\n"
                          "Approfitta di questo periodo per rafforzare la squadra!"
            },
            "Prima Parte Campionato": {
                "title": "⚽ Campionati Iniziati!",
                "message": "I campionati sono ufficialmente iniziati!\n\n"
                          "È tempo di mettere alla prova la tua squadra.\n"
                          "Buona fortuna per la stagione!"
            },
            "Calciomercato Invernale": {
                "title": "❄️ Calciomercato Invernale Aperto!",
                "message": "Il calciomercato invernale è ora aperto!\n\n"
                          "Ultima possibilità per rinforzare la squadra.\n"
                          "Valuta attentamente le tue mosse!"
            },
            "Seconda Parte Campionato": {
                "title": "🏃 Sprint Finale!",
                "message": "Inizia la seconda parte del campionato!\n\n"
                          "È il momento decisivo della stagione.\n"
                          "Ogni partita conta per raggiungere i tuoi obiettivi!"
            },
            "Playoff e Coppe": {
                "title": "🏆 Fase Finale!",
                "message": "Playoff e finali di coppa in corso!\n\n"
                          "Le squadre si giocano tutto.\n"
                          "Chi riuscirà a conquistare la gloria?"
            },
            "Pausa Estiva": {
                "title": "🏖️ Pausa Estiva",
                "message": "La stagione è terminata!\n\n"
                          "È tempo di riposare e pianificare.\n"
                          "Presto inizierà una nuova stagione!"
            }
        }
        
        phase_name = new_phase.value
        if phase_name in phase_messages:
            msg_info = phase_messages[phase_name]
            QMessageBox.information(self, msg_info["title"], msg_info["message"])
