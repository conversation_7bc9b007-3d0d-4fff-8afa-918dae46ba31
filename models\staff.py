"""
Modello per rappresentare lo staff del club (allenatore, direttore sportivo, etc.)
"""

from dataclasses import dataclass
from typing import Dict, Optional
from enum import Enum
import random

class StaffRole(Enum):
    """Ruoli dello staff"""
    ALLENATORE = "allenatore"
    DIRETTORE_SPORTIVO = "direttore_sportivo"
    ALLENATORE_PORTIERI = "allenatore_portieri"
    PREPARATORE_ATLETICO = "preparatore_atletico"
    MEDICO = "medico"

class StaffStatus(Enum):
    """Stato dello staff"""
    ATTIVO = "attivo"
    SOSPESO = "sospeso"
    IN_TRATTATIVA = "in_trattativa"

@dataclass
class StaffStats:
    """Statistiche dello staff"""
    tattica: int = 50
    motivazione: int = 50
    gestione_gruppo: int = 50
    esperienza: int = 50
    reputazione: int = 50
    
    def overall(self) -> int:
        """Calcola il valore complessivo dello staff"""
        return int((self.tattica + self.motivazione + self.gestione_gruppo + 
                   self.esperienza + self.reputazione) / 5)

class StaffMember:
    """Classe che rappresenta un membro dello staff"""
    
    def __init__(self,
                 staff_id: int,
                 nome: str,
                 cognome: str,
                 eta: int,
                 ruolo: StaffRole,
                 nazionalita: str = "Italia",
                 stipendio: int = 50000,
                 contratto_scadenza: int = 2025):
        
        self.id = staff_id
        self.nome = nome
        self.cognome = cognome
        self.eta = eta
        self.ruolo = ruolo
        self.nazionalita = nazionalita
        self.stipendio = stipendio
        self.contratto_scadenza = contratto_scadenza
        
        # Statistiche
        self.stats = StaffStats()
        self._generate_stats_by_role_and_age()
        
        # Stato
        self.status = StaffStatus.ATTIVO
        self.morale = random.randint(70, 90)
        
        # Statistiche carriera
        self.anni_esperienza = max(0, self.eta - 30)
        self.squadre_allenate = random.randint(1, max(1, self.anni_esperienza // 3))
        self.trofei_vinti = random.randint(0, max(0, self.anni_esperienza // 5))
        
        # Preferenze tattiche (solo per allenatori)
        if self.ruolo == StaffRole.ALLENATORE:
            self.modulo_preferito = self._get_random_formation()
            self.stile_gioco = self._get_random_play_style()
    
    def _generate_stats_by_role_and_age(self):
        """Genera statistiche basate su ruolo ed età"""
        base_stats = self._get_base_stats_by_role()
        age_modifier = self._get_age_modifier()
        
        for stat_name, base_value in base_stats.items():
            modified_value = int(base_value * age_modifier)
            modified_value = max(20, min(99, modified_value))
            setattr(self.stats, stat_name, modified_value)
    
    def _get_base_stats_by_role(self) -> Dict[str, int]:
        """Restituisce statistiche base per ruolo"""
        if self.ruolo == StaffRole.ALLENATORE:
            return {
                'tattica': random.randint(60, 90),
                'motivazione': random.randint(65, 90),
                'gestione_gruppo': random.randint(60, 85),
                'esperienza': random.randint(40, 80),
                'reputazione': random.randint(30, 70)
            }
        elif self.ruolo == StaffRole.DIRETTORE_SPORTIVO:
            return {
                'tattica': random.randint(50, 75),
                'motivazione': random.randint(55, 80),
                'gestione_gruppo': random.randint(70, 90),
                'esperienza': random.randint(50, 85),
                'reputazione': random.randint(40, 75)
            }
        else:  # Altri ruoli
            return {
                'tattica': random.randint(40, 70),
                'motivazione': random.randint(50, 80),
                'gestione_gruppo': random.randint(45, 75),
                'esperienza': random.randint(40, 80),
                'reputazione': random.randint(30, 65)
            }
    
    def _get_age_modifier(self) -> float:
        """Restituisce modificatore basato sull'età"""
        if self.eta < 35:
            return 0.8  # Giovani ma inesperti
        elif self.eta < 45:
            return random.uniform(0.9, 1.1)  # In crescita
        elif self.eta < 55:
            return random.uniform(1.0, 1.2)  # Nel pieno della carriera
        elif self.eta < 65:
            return random.uniform(0.95, 1.1)  # Esperti ma meno energici
        else:
            return random.uniform(0.8, 1.0)  # Veterani
    
    def _get_random_formation(self) -> str:
        """Restituisce un modulo casuale"""
        formations = ["4-4-2", "4-3-3", "3-5-2", "4-2-3-1", "5-3-2", "4-5-1", "3-4-3"]
        return random.choice(formations)
    
    def _get_random_play_style(self) -> str:
        """Restituisce uno stile di gioco casuale"""
        styles = ["Offensivo", "Difensivo", "Possesso palla", "Contropiede", 
                 "Pressing alto", "Gioco diretto", "Tiki-taka"]
        return random.choice(styles)
    
    def nome_completo(self) -> str:
        """Restituisce nome e cognome"""
        return f"{self.nome} {self.cognome}"
    
    def get_salary_demand(self) -> int:
        """Calcola la richiesta di stipendio basata sulle statistiche"""
        base_salary = self.stats.overall() * 1000
        
        # Modificatori per ruolo
        if self.ruolo == StaffRole.ALLENATORE:
            role_multiplier = 3.0
        elif self.ruolo == StaffRole.DIRETTORE_SPORTIVO:
            role_multiplier = 2.0
        else:
            role_multiplier = 1.0
        
        # Modificatori per esperienza e trofei
        experience_bonus = self.trofei_vinti * 10000
        
        return int(base_salary * role_multiplier) + experience_bonus
    
    def is_happy_with_salary(self, offered_salary: int) -> bool:
        """Verifica se è soddisfatto dello stipendio offerto"""
        demanded_salary = self.get_salary_demand()
        return offered_salary >= demanded_salary * 0.8  # Accetta anche l'80% della richiesta
    
    def update_weekly(self):
        """Aggiorna lo stato dello staff settimanalmente"""
        # Aggiorna morale leggermente
        self.morale = max(50, min(100, self.morale + random.randint(-2, 2)))
        
        # Possibile miglioramento delle statistiche con l'esperienza
        if random.random() < 0.01:  # 1% di possibilità a settimana
            stat_to_improve = random.choice(['tattica', 'motivazione', 'gestione_gruppo'])
            current_value = getattr(self.stats, stat_to_improve)
            if current_value < 95:
                setattr(self.stats, stat_to_improve, current_value + 1)
    
    def get_team_bonus(self) -> Dict[str, float]:
        """Restituisce i bonus che questo membro dello staff dà alla squadra"""
        bonuses = {}
        
        if self.ruolo == StaffRole.ALLENATORE:
            bonuses['tattica'] = self.stats.tattica / 100.0
            bonuses['morale'] = self.stats.motivazione / 100.0
            bonuses['disciplina'] = self.stats.gestione_gruppo / 100.0
        elif self.ruolo == StaffRole.DIRETTORE_SPORTIVO:
            bonuses['scouting'] = self.stats.esperienza / 100.0
            bonuses['trattative'] = self.stats.gestione_gruppo / 100.0
        elif self.ruolo == StaffRole.PREPARATORE_ATLETICO:
            bonuses['forma_fisica'] = self.stats.esperienza / 100.0
            bonuses['infortuni'] = (100 - self.stats.esperienza) / 100.0  # Meno infortuni
        elif self.ruolo == StaffRole.MEDICO:
            bonuses['recupero_infortuni'] = self.stats.esperienza / 100.0
        
        return bonuses
    
    def __str__(self):
        return f"{self.nome_completo()} - {self.ruolo.value.title()} (Overall: {self.stats.overall()})"
