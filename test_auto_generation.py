#!/usr/bin/env python3
"""
Test per verificare la generazione automatica delle rose
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.club import Club, League
from models.player import Position
from core.player_generator import player_generator
from utils.json_loader import data_loader
from gui.club_selection import ClubLoadingThread

def test_auto_generation():
    """Test della generazione automatica delle rose"""
    print("🚀 Test Generazione Automatica Rose")
    print("=" * 50)
    
    # Simula il caricamento come fa l'interfaccia
    leagues_data = {
        "Serie A": ("serie_a.csv", League.SERIE_A),
        "Serie B": ("serie_b.csv", League.SERIE_B),
        "Serie C Girone A": ("serie_c_girone_A.csv", League.SERIE_C),
        "Serie C Girone B": ("serie_c_girone_B.csv", League.SERIE_C),
        "Serie C Girone C": ("serie_c_girone_C.csv", League.SERIE_C)
    }
    
    all_clubs = []
    total_players = 0
    
    print("📊 Caricamento squadre e generazione rose...")
    
    for league_name, (filename, league_enum) in leagues_data.items():
        print(f"\n🔄 {league_name}:")
        
        # Carica dati dal CSV
        teams_data = data_loader.load_csv(filename)
        
        if teams_data:
            # Crea club
            clubs = []
            for i, team_data in enumerate(teams_data):
                try:
                    # Determina budget basato sul campionato
                    base_budget = {
                        League.SERIE_A: 30000000,
                        League.SERIE_B: 8000000,
                        League.SERIE_C: 2000000
                    }.get(league_enum, 1000000)
                    
                    import random
                    budget_variation = base_budget * 0.3
                    budget = int(base_budget + random.uniform(-budget_variation, budget_variation))
                    
                    # Capacità stadio
                    capacita = int(str(team_data.get('capienza_stadio', '10000')).replace(',', '').replace('.', ''))
                    if capacita == 0:
                        capacita = {
                            League.SERIE_A: 25000,
                            League.SERIE_B: 15000,
                            League.SERIE_C: 8000
                        }.get(league_enum, 5000)
                    
                    nome_club = team_data.get('squadra', 'Club Sconosciuto').strip()
                    
                    club = Club(
                        club_id=len(all_clubs) + 1,
                        nome=nome_club,
                        citta=nome_club,  # Usa il nome come città se non disponibile
                        campionato=league_enum,
                        budget=budget,
                        stadio_nome=team_data.get('stadio', f"Stadio {nome_club}").strip(),
                        stadio_capacita=capacita
                    )
                    
                    # Genera la rosa automaticamente
                    squad = player_generator.generate_squad(club)
                    for player in squad:
                        club.add_player(player)
                    
                    clubs.append(club)
                    total_players += len(squad)
                    
                    print(f"   ✅ {club.nome}: {len(squad)} giocatori (Overall: {club.get_team_overall()})")
                    
                except Exception as e:
                    print(f"   ❌ Errore con {team_data}: {e}")
            
            all_clubs.extend(clubs)
            print(f"   📈 Totale {league_name}: {len(clubs)} squadre")
    
    print("\n" + "=" * 50)
    print("📊 STATISTICHE FINALI")
    print("=" * 50)
    
    print(f"🏟️  Squadre totali: {len(all_clubs)}")
    print(f"⚽ Giocatori totali: {total_players}")
    print(f"📊 Media giocatori per squadra: {total_players / len(all_clubs):.1f}")
    
    # Statistiche per campionato
    for league in [League.SERIE_A, League.SERIE_B, League.SERIE_C]:
        league_clubs = [c for c in all_clubs if c.campionato == league]
        if league_clubs:
            avg_overall = sum(c.get_team_overall() for c in league_clubs) / len(league_clubs)
            avg_budget = sum(c.budget for c in league_clubs) / len(league_clubs)
            print(f"📈 {league.value}: {len(league_clubs)} squadre, Overall medio: {avg_overall:.1f}, Budget medio: €{avg_budget:,.0f}")
    
    # Verifica distribuzione posizioni
    print("\n🎯 DISTRIBUZIONE POSIZIONI:")
    total_by_position = {pos: 0 for pos in Position}
    
    for club in all_clubs:
        for player in club.giocatori:
            total_by_position[player.posizione] += 1
    
    for position, count in total_by_position.items():
        avg_per_team = count / len(all_clubs)
        print(f"   {position.value}: {count} totali ({avg_per_team:.1f} per squadra)")
    
    # Top 5 squadre per overall
    print("\n🏆 TOP 5 SQUADRE PER OVERALL:")
    top_clubs = sorted(all_clubs, key=lambda c: c.get_team_overall(), reverse=True)[:5]
    for i, club in enumerate(top_clubs, 1):
        print(f"   {i}. {club.nome} ({club.campionato.value}): Overall {club.get_team_overall()}")
    
    # Verifica che tutte le squadre abbiano giocatori
    clubs_without_players = [c for c in all_clubs if len(c.giocatori) == 0]
    if clubs_without_players:
        print(f"\n❌ ERRORE: {len(clubs_without_players)} squadre senza giocatori!")
        for club in clubs_without_players:
            print(f"   - {club.nome}")
    else:
        print(f"\n✅ SUCCESSO: Tutte le {len(all_clubs)} squadre hanno una rosa completa!")
    
    print("\n🎮 Il sistema di generazione automatica funziona perfettamente!")
    return len(clubs_without_players) == 0

if __name__ == "__main__":
    success = test_auto_generation()
    sys.exit(0 if success else 1)
