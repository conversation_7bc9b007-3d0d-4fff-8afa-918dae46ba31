#!/usr/bin/env python3
"""
Test per verificare le squadre europee e il mercato internazionale
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.club import Club, League
from models.player import Position
from core.player_generator import player_generator
from utils.json_loader import data_loader

def test_european_clubs():
    """Test delle squadre europee"""
    print("🌍 Test Squadre Europee per Calciomercato")
    print("=" * 60)
    
    # Carica squadre europee
    teams_data = data_loader.load_csv("altre_europa.csv")
    
    if not teams_data:
        print("❌ Errore: File altre_europa.csv non trovato")
        return False
    
    print(f"📊 Caricate {len(teams_data)} squadre europee")
    
    # Crea club europei con rose
    european_clubs = []
    
    for i, team_data in enumerate(teams_data[:10]):  # Test prime 10 per velocità
        try:
            # Budget dal CSV
            budget_mln = float(team_data.get('budget_mln', '50'))
            budget = int(budget_mln * 1000000)
            
            # Capacità stadio
            capacita = int(team_data.get('capienza_stadio', '30000').replace(',', '').replace('.', ''))
            
            nome_club = team_data.get('squadra', 'Club Sconosciuto').strip()
            
            club = Club(
                club_id=i + 1,
                nome=nome_club,
                citta=nome_club,
                campionato=League.EUROPA,
                budget=budget,
                stadio_nome=team_data.get('stadio', f"Stadio {nome_club}").strip(),
                stadio_capacita=capacita
            )
            
            # Genera la rosa
            squad = player_generator.generate_squad(club)
            for player in squad:
                club.add_player(player)
            
            european_clubs.append(club)
            
        except Exception as e:
            print(f"❌ Errore con {team_data}: {e}")
    
    print(f"\n✅ Processate {len(european_clubs)} squadre europee")
    
    # Analisi dettagliata
    print("\n" + "=" * 60)
    print("📈 ANALISI SQUADRE EUROPEE")
    print("=" * 60)
    
    # Top 5 per budget
    top_budget = sorted(european_clubs, key=lambda c: c.budget, reverse=True)[:5]
    print("\n💰 TOP 5 PER BUDGET:")
    for i, club in enumerate(top_budget, 1):
        print(f"   {i}. {club.nome}: €{club.budget:,} (Overall: {club.get_team_overall()})")
    
    # Top 5 per overall
    top_overall = sorted(european_clubs, key=lambda c: c.get_team_overall(), reverse=True)[:5]
    print("\n🏆 TOP 5 PER OVERALL SQUADRA:")
    for i, club in enumerate(top_overall, 1):
        print(f"   {i}. {club.nome}: Overall {club.get_team_overall()} (Budget: €{club.budget:,})")
    
    # Analisi nazionalità giocatori
    print("\n🌍 ANALISI NAZIONALITÀ:")
    nationality_count = {}
    total_players = 0
    
    for club in european_clubs:
        for player in club.giocatori:
            nationality_count[player.nazionalita] = nationality_count.get(player.nazionalita, 0) + 1
            total_players += 1
    
    # Top 10 nazionalità
    top_nationalities = sorted(nationality_count.items(), key=lambda x: x[1], reverse=True)[:10]
    for nationality, count in top_nationalities:
        percentage = (count / total_players) * 100
        print(f"   {nationality}: {count} giocatori ({percentage:.1f}%)")
    
    # Analisi per club specifico
    print("\n" + "=" * 60)
    print("🔍 ANALISI DETTAGLIATA CLUB SPECIFICI")
    print("=" * 60)
    
    # Analizza Real Madrid se presente
    real_madrid = next((c for c in european_clubs if "Real Madrid" in c.nome), None)
    if real_madrid:
        print(f"\n⚪ {real_madrid.nome}:")
        print(f"   Budget: €{real_madrid.budget:,}")
        print(f"   Overall squadra: {real_madrid.get_team_overall()}")
        print(f"   Stadio: {real_madrid.stadio_nome} ({real_madrid.stadio_capacita:,} posti)")
        print(f"   Entrate mensili: €{real_madrid.entrate_mensili:,}")
        print(f"   Reputazione: {real_madrid.reputazione}")
        
        # Top 5 giocatori
        top_players = sorted(real_madrid.giocatori, key=lambda p: p.stats.overall(), reverse=True)[:5]
        print("   Top 5 giocatori:")
        for player in top_players:
            print(f"     • {player.nome_completo()} ({player.posizione.value}) - Overall: {player.stats.overall()}, Valore: €{player.valore_mercato:,}")
    
    # Analizza Manchester City se presente
    man_city = next((c for c in european_clubs if "Manchester City" in c.nome), None)
    if man_city:
        print(f"\n🔵 {man_city.nome}:")
        print(f"   Budget: €{man_city.budget:,}")
        print(f"   Overall squadra: {man_city.get_team_overall()}")
        print(f"   Stadio: {man_city.stadio_nome} ({man_city.stadio_capacita:,} posti)")
        
        # Nazionalità nella rosa
        nationalities = {}
        for player in man_city.giocatori:
            nationalities[player.nazionalita] = nationalities.get(player.nazionalita, 0) + 1
        
        print("   Nazionalità in rosa:")
        for nat, count in sorted(nationalities.items(), key=lambda x: x[1], reverse=True):
            print(f"     • {nat}: {count} giocatori")
    
    # Confronto livelli
    print("\n📊 CONFRONTO LIVELLI CLUB:")
    
    # Raggruppa per livello di budget
    world_class = [c for c in european_clubs if c.budget > 500000000]
    elite = [c for c in european_clubs if 200000000 < c.budget <= 500000000]
    top = [c for c in european_clubs if 100000000 < c.budget <= 200000000]
    medium = [c for c in european_clubs if c.budget <= 100000000]
    
    print(f"   🌟 World Class (>€500M): {len(world_class)} squadre")
    if world_class:
        avg_overall = sum(c.get_team_overall() for c in world_class) / len(world_class)
        print(f"      Overall medio: {avg_overall:.1f}")
    
    print(f"   ⭐ Elite (€200M-500M): {len(elite)} squadre")
    if elite:
        avg_overall = sum(c.get_team_overall() for c in elite) / len(elite)
        print(f"      Overall medio: {avg_overall:.1f}")
    
    print(f"   🔥 Top (€100M-200M): {len(top)} squadre")
    if top:
        avg_overall = sum(c.get_team_overall() for c in top) / len(top)
        print(f"      Overall medio: {avg_overall:.1f}")
    
    print(f"   ⚽ Medium (<€100M): {len(medium)} squadre")
    if medium:
        avg_overall = sum(c.get_team_overall() for c in medium) / len(medium)
        print(f"      Overall medio: {avg_overall:.1f}")
    
    # Potenziale di mercato
    print("\n💼 POTENZIALE CALCIOMERCATO:")
    
    # Giocatori più costosi
    all_players = []
    for club in european_clubs:
        for player in club.giocatori:
            all_players.append((player, club))
    
    top_valuable = sorted(all_players, key=lambda x: x[0].valore_mercato, reverse=True)[:10]
    print("\n💎 TOP 10 GIOCATORI PIÙ COSTOSI:")
    for i, (player, club) in enumerate(top_valuable, 1):
        print(f"   {i}. {player.nome_completo()} ({club.nome}) - €{player.valore_mercato:,}")
    
    print(f"\n🎯 MERCATO TOTALE:")
    total_value = sum(p.valore_mercato for p, _ in all_players)
    total_salaries = sum(p.stipendio for p, _ in all_players)
    print(f"   Valore totale giocatori: €{total_value:,}")
    print(f"   Stipendi totali mensili: €{total_salaries:,}")
    
    print("\n✅ Test squadre europee completato con successo!")
    print("🌍 Il mercato internazionale è pronto per il calciomercato!")
    
    return True

if __name__ == "__main__":
    success = test_european_clubs()
    sys.exit(0 if success else 1)
