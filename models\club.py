"""
Modello per rappresentare un club di calcio
"""

from typing import List, Dict, Optional
from enum import Enum
import random

from .player import Player, Position
from .staff import StaffMember, StaffRole

class League(Enum):
    """Campionati"""
    SERIE_A = "Serie A"
    SERIE_B = "Serie B"
    SERIE_C = "Serie C"
    EUROPA = "Europa"  # Per squadre straniere

class ClubStatus(Enum):
    """Stato del club"""
    ATTIVO = "attivo"
    FALLIMENTO = "fallimento"
    VENDITA = "in_vendita"

class Club:
    """Classe che rappresenta un club di calcio"""
    
    def __init__(self,
                 club_id: int,
                 nome: str,
                 citta: str,
                 campionato: League,
                 budget: int = 1000000,
                 stadio_nome: str = "",
                 stadio_capacita: int = 10000):
        
        self.id = club_id
        self.nome = nome
        self.citta = citta
        self.campionato = campionato
        self.budget = budget
        self.stadio_nome = stadio_nome or f"Stadio {nome}"
        self.stadio_capacita = stadio_capacita
        
        # Rosa e staff
        self.giocatori: List[Player] = []
        self.staff: List[StaffMember] = []
        
        # Finanze
        self.debiti = 0
        self.entrate_mensili = self._calculate_base_income()
        self.uscite_mensili = 0
        
        # Infrastrutture
        self.centro_sportivo_livello = 1
        self.settore_giovanile_livello = 1
        self.centro_medico_livello = 1
        
        # Statistiche stagionali
        self.punti = 0
        self.partite_giocate = 0
        self.vittorie = 0
        self.pareggi = 0
        self.sconfitte = 0
        self.gol_fatti = 0
        self.gol_subiti = 0
        
        # Obiettivi stagionali
        self.obiettivo_principale = self._get_season_objective()
        self.obiettivi_secondari = self._get_secondary_objectives()
        
        # Tifosi e reputazione
        self.tifosi_soddisfazione = random.randint(60, 80)
        self.reputazione = self._calculate_base_reputation()
        
        # Sponsor
        self.sponsor_principale = None
        self.sponsor_tecnico = None
        self.altri_sponsor = []
        
        self.status = ClubStatus.ATTIVO
    
    def _calculate_base_income(self) -> int:
        """Calcola le entrate base mensili"""
        base_income = {
            League.SERIE_A: 2000000,
            League.SERIE_B: 500000,
            League.SERIE_C: 100000,
            League.EUROPA: int(self.budget * 0.05)  # 5% del budget mensile per squadre europee
        }
        return base_income.get(self.campionato, 50000)
    
    def _calculate_base_reputation(self) -> int:
        """Calcola la reputazione base"""
        if self.campionato == League.EUROPA:
            # Reputazione basata sul budget per squadre europee
            if self.budget > 500000000:
                return random.randint(95, 99)  # World class
            elif self.budget > 200000000:
                return random.randint(85, 95)  # Elite
            elif self.budget > 100000000:
                return random.randint(75, 85)  # Top
            else:
                return random.randint(60, 75)  # Good
        else:
            base_reputation = {
                League.SERIE_A: random.randint(70, 90),
                League.SERIE_B: random.randint(50, 70),
                League.SERIE_C: random.randint(30, 50)
            }
            return base_reputation.get(self.campionato, 30)
    
    def _get_season_objective(self) -> str:
        """Determina l'obiettivo principale della stagione"""
        if self.campionato == League.SERIE_A:
            objectives = ["Salvezza", "Metà classifica", "Europa League", "Champions League", "Scudetto"]
            weights = [30, 40, 20, 8, 2]
        elif self.campionato == League.SERIE_B:
            objectives = ["Salvezza", "Playoff", "Promozione diretta"]
            weights = [50, 35, 15]
        else:  # Serie C
            objectives = ["Salvezza", "Playoff", "Promozione diretta"]
            weights = [60, 30, 10]
        
        return random.choices(objectives, weights=weights)[0]
    
    def _get_secondary_objectives(self) -> List[str]:
        """Determina gli obiettivi secondari"""
        all_objectives = [
            "Valorizzare giovani",
            "Pareggio di bilancio",
            "Migliorare stadio",
            "Coppa Italia",
            "Fair play"
        ]
        return random.sample(all_objectives, random.randint(1, 3))
    
    def add_player(self, player: Player):
        """Aggiunge un giocatore alla rosa"""
        self.giocatori.append(player)
        self.uscite_mensili += player.stipendio
    
    def remove_player(self, player: Player):
        """Rimuove un giocatore dalla rosa"""
        if player in self.giocatori:
            self.giocatori.remove(player)
            self.uscite_mensili -= player.stipendio
    
    def add_staff(self, staff_member: StaffMember):
        """Aggiunge un membro dello staff"""
        # Rimuovi eventuale staff dello stesso ruolo
        self.staff = [s for s in self.staff if s.ruolo != staff_member.ruolo]
        self.staff.append(staff_member)
        self.uscite_mensili += staff_member.stipendio
    
    def remove_staff(self, staff_member: StaffMember):
        """Rimuove un membro dello staff"""
        if staff_member in self.staff:
            self.staff.remove(staff_member)
            self.uscite_mensili -= staff_member.stipendio
    
    def get_players_by_position(self, position: Position) -> List[Player]:
        """Restituisce i giocatori per posizione"""
        return [p for p in self.giocatori if p.posizione == position]
    
    def get_available_players(self) -> List[Player]:
        """Restituisce i giocatori disponibili"""
        return [p for p in self.giocatori if p.is_available()]
    
    def get_staff_by_role(self, role: StaffRole) -> Optional[StaffMember]:
        """Restituisce un membro dello staff per ruolo"""
        for staff_member in self.staff:
            if staff_member.ruolo == role:
                return staff_member
        return None
    
    def get_team_overall(self) -> int:
        """Calcola l'overall medio della squadra"""
        if not self.giocatori:
            return 50
        
        total_overall = sum(player.stats.overall() for player in self.giocatori)
        return int(total_overall / len(self.giocatori))
    
    def get_monthly_balance(self) -> int:
        """Calcola il bilancio mensile"""
        return self.entrate_mensili - self.uscite_mensili
    
    def can_afford_player(self, player_cost: int, player_salary: int) -> bool:
        """Verifica se il club può permettersi un giocatore"""
        total_cost = player_cost + (player_salary * 12)  # Costo + stipendio annuale
        return self.budget >= total_cost and self.get_monthly_balance() - player_salary > 0
    
    def update_weekly(self):
        """Aggiorna lo stato del club settimanalmente"""
        # Aggiorna giocatori e staff
        for player in self.giocatori:
            player.update_weekly()
        
        for staff_member in self.staff:
            staff_member.update_weekly()
        
        # Aggiorna soddisfazione tifosi
        self._update_fan_satisfaction()
        
        # Possibili eventi casuali
        self._random_events()
    
    def _update_fan_satisfaction(self):
        """Aggiorna la soddisfazione dei tifosi"""
        # Basato su risultati recenti, obiettivi, e gestione
        if self.partite_giocate > 0:
            win_rate = self.vittorie / self.partite_giocate
            if win_rate > 0.6:
                self.tifosi_soddisfazione = min(100, self.tifosi_soddisfazione + 2)
            elif win_rate < 0.3:
                self.tifosi_soddisfazione = max(0, self.tifosi_soddisfazione - 2)
    
    def _random_events(self):
        """Eventi casuali che possono accadere al club"""
        if random.random() < 0.05:  # 5% di possibilità a settimana
            events = [
                "sponsor_interest",
                "fan_protest",
                "media_attention",
                "infrastructure_issue"
            ]
            event = random.choice(events)
            # Qui si potrebbero implementare gli effetti degli eventi
    
    def get_formation_players(self, formation: str = "4-4-2") -> Dict[str, List[Player]]:
        """Restituisce i giocatori per una formazione specifica"""
        available_players = self.get_available_players()
        
        formation_dict = {
            "portieri": [p for p in available_players if p.posizione == Position.PORTIERE][:1],
            "difensori": [p for p in available_players if p.posizione == Position.DIFENSORE][:4],
            "centrocampisti": [p for p in available_players if p.posizione == Position.CENTROCAMPISTA][:4],
            "attaccanti": [p for p in available_players if p.posizione == Position.ATTACCANTE][:2]
        }
        
        return formation_dict
    
    def __str__(self):
        return f"{self.nome} ({self.campionato.value}) - Budget: €{self.budget:,}"
