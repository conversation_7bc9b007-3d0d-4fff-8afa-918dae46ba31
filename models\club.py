"""
Modello per rappresentare un club di calcio
"""

from typing import List, Dict, Optional
from enum import Enum
import random

from .player import Player, Position
from .staff import StaffMember, StaffRole

class League(Enum):
    """Campionati"""
    SERIE_A = "Serie A"
    SERIE_B = "Serie B"
    SERIE_C = "Serie C"
    EUROPA = "Europa"  # Per squadre straniere

class ClubStatus(Enum):
    """Stato del club"""
    ATTIVO = "attivo"
    FALLIMENTO = "fallimento"
    VENDITA = "in_vendita"

class Club:
    """Classe che rappresenta un club di calcio"""
    
    def __init__(self,
                 club_id: int,
                 nome: str,
                 citta: str,
                 campionato: League,
                 budget: int = 1000000,
                 stadio_nome: str = "",
                 stadio_capacita: int = 10000,
                 budget_mln: float = None,
                 attacco: int = 70,
                 difesa: int = 70,
                 fisico: int = 70,
                 velocita: int = 70):
        
        self.id = club_id
        self.nome = nome
        self.citta = citta
        self.campionato = campionato
        self.budget = budget
        self.stadio_nome = stadio_nome or f"Stadio {nome}"
        self.stadio_capacita = stadio_capacita

        # Budget dettagliato
        self.budget_mln = budget_mln or (budget / 1000000)  # Converti in milioni se non fornito
        self.budget_trasferimenti = self._calculate_transfer_budget()
        self.budget_stipendi = self._calculate_salary_budget()

        # Statistiche tecniche dai CSV
        self.attacco = attacco
        self.difesa = difesa
        self.fisico = fisico
        self.velocita = velocita
        
        # Rosa e staff
        self.giocatori: List[Player] = []
        self.staff: List[StaffMember] = []
        
        # Finanze
        self.debiti = 0
        self.entrate_mensili = self._calculate_base_income()
        self.uscite_mensili = 0
        
        # Infrastrutture
        self.centro_sportivo_livello = 1
        self.settore_giovanile_livello = 1
        self.centro_medico_livello = 1
        
        # Statistiche stagionali
        self.punti = 0
        self.partite_giocate = 0
        self.vittorie = 0
        self.pareggi = 0
        self.sconfitte = 0
        self.gol_fatti = 0
        self.gol_subiti = 0
        
        # Obiettivi stagionali basati sul budget
        self.obiettivo_principale = self._get_season_objective_by_budget()
        self.obiettivi_secondari = self._get_secondary_objectives()
        
        # Tifosi e reputazione
        self.tifosi_soddisfazione = random.randint(60, 80)
        self.reputazione = self._calculate_base_reputation()
        
        # Sponsor
        self.sponsor_principale = None
        self.sponsor_tecnico = None
        self.altri_sponsor = []
        
        self.status = ClubStatus.ATTIVO

    def _calculate_transfer_budget(self) -> int:
        """Calcola il budget disponibile per i trasferimenti"""
        # Percentuale del budget totale dedicata ai trasferimenti
        if self.campionato == League.EUROPA:
            transfer_percentage = 0.4  # 40% per squadre europee
        elif self.campionato == League.SERIE_A:
            transfer_percentage = 0.35  # 35% per Serie A
        elif self.campionato == League.SERIE_B:
            transfer_percentage = 0.3   # 30% per Serie B
        else:  # Serie C
            transfer_percentage = 0.25  # 25% per Serie C

        return int(self.budget * transfer_percentage)

    def _calculate_salary_budget(self) -> int:
        """Calcola il budget mensile per gli stipendi"""
        # Percentuale del budget totale dedicata agli stipendi (annuale)
        if self.campionato == League.EUROPA:
            salary_percentage = 0.5   # 50% per squadre europee
        elif self.campionato == League.SERIE_A:
            salary_percentage = 0.45  # 45% per Serie A
        elif self.campionato == League.SERIE_B:
            salary_percentage = 0.4   # 40% per Serie B
        else:  # Serie C
            salary_percentage = 0.35  # 35% per Serie C

        annual_salary_budget = int(self.budget * salary_percentage)
        return annual_salary_budget // 12  # Budget mensile
    
    def _calculate_base_income(self) -> int:
        """Calcola le entrate base mensili"""
        base_income = {
            League.SERIE_A: 2000000,
            League.SERIE_B: 500000,
            League.SERIE_C: 100000,
            League.EUROPA: int(self.budget * 0.05)  # 5% del budget mensile per squadre europee
        }
        return base_income.get(self.campionato, 50000)
    
    def _calculate_base_reputation(self) -> int:
        """Calcola la reputazione base"""
        if self.campionato == League.EUROPA:
            # Reputazione basata sul budget per squadre europee
            if self.budget > 500000000:
                return random.randint(95, 99)  # World class
            elif self.budget > 200000000:
                return random.randint(85, 95)  # Elite
            elif self.budget > 100000000:
                return random.randint(75, 85)  # Top
            else:
                return random.randint(60, 75)  # Good
        else:
            base_reputation = {
                League.SERIE_A: random.randint(70, 90),
                League.SERIE_B: random.randint(50, 70),
                League.SERIE_C: random.randint(30, 50)
            }
            return base_reputation.get(self.campionato, 30)
    
    def _get_season_objective_by_budget(self) -> str:
        """Determina l'obiettivo principale basato su multiple variabili"""
        # Calcola un punteggio complessivo considerando vari fattori
        objective_score = self._calculate_objective_score()

        if self.campionato == League.EUROPA:
            return self._get_european_objective(objective_score)
        elif self.campionato == League.SERIE_A:
            return self._get_serie_a_objective(objective_score)
        elif self.campionato == League.SERIE_B:
            return self._get_serie_b_objective(objective_score)
        else:  # Serie C
            return self._get_serie_c_objective(objective_score)

    def _calculate_objective_score(self) -> float:
        """Calcola un punteggio per determinare l'obiettivo basato sui dati reali dei CSV"""
        score = 0.0

        # 1. Fattore Budget (peso 50% - principale)
        budget_score = self._get_budget_score()
        score += budget_score * 0.50

        # 2. Fattore Stadio (peso 30% - importante per le aspettative)
        stadium_score = self._get_stadium_score_from_data()
        score += stadium_score * 0.30

        # 3. Fattore Tecnico (peso 15% - basato su statistiche CSV)
        technical_score = self._get_technical_score()
        score += technical_score * 0.15

        # 4. Fattore Casuale (peso 5% - minimo per variabilità)
        random_score = random.uniform(-0.5, 1.0)
        score += random_score * 0.05

        return max(0, score)  # Non può essere negativo

    def _get_budget_score(self) -> float:
        """Calcola il punteggio budget basato sui dati reali dei CSV"""
        # Analisi dei range di budget reali dai CSV:
        # Serie A: 210-600M, Serie B: 23-48M, Serie C: 5-30M

        if self.campionato == League.SERIE_A:
            # Range Serie A: 210-600M
            if self.budget_mln >= 500:
                return 10.0  # Top assoluto (Inter, Milan, Juventus)
            elif self.budget_mln >= 400:
                return 8.5   # Elite (Napoli, Atalanta, Roma)
            elif self.budget_mln >= 300:
                return 7.0   # Buone (Bologna, Torino, Monza)
            elif self.budget_mln >= 250:
                return 5.5   # Medie (Lecce, Udinese, Empoli)
            else:
                return 4.0   # Salvezza (Frosinone, Salernitana)

        elif self.campionato == League.SERIE_B:
            # Range Serie B: 23-48M
            if self.budget_mln >= 40:
                return 6.0   # Top Serie B (Parma, Venezia, Sampdoria)
            elif self.budget_mln >= 30:
                return 4.5   # Playoff (Palermo, Bari, Pisa)
            elif self.budget_mln >= 25:
                return 3.0   # Metà classifica
            else:
                return 2.0   # Salvezza

        elif self.campionato == League.SERIE_C:
            # Range Serie C: 5-30M
            if self.budget_mln >= 20:
                return 4.0   # Top Serie C (Cesena, Catania)
            elif self.budget_mln >= 15:
                return 3.0   # Playoff
            elif self.budget_mln >= 10:
                return 2.0   # Metà classifica
            else:
                return 1.0   # Salvezza

        else:  # Europa
            # Range Europa molto variabile
            if self.budget_mln >= 500:
                return 10.0
            elif self.budget_mln >= 200:
                return 8.0
            elif self.budget_mln >= 100:
                return 6.0
            else:
                return 4.0

    def _get_stadium_score_from_data(self) -> float:
        """Calcola il punteggio stadio basato sui dati reali dei CSV"""
        # Analisi delle capacità reali dai CSV:
        # Serie A: 10K-80K, Serie B: 5K-58K, Serie C: 1.5K-38K

        if self.campionato == League.SERIE_A:
            # Range Serie A: 10K-80K
            if self.stadio_capacita >= 70000:
                return 4.0  # San Siro, Olimpico (Inter, Milan, Roma, Lazio)
            elif self.stadio_capacita >= 50000:
                return 3.5  # Maradona (Napoli)
            elif self.stadio_capacita >= 35000:
                return 3.0  # Dall'Ara, Franchi (Bologna, Fiorentina)
            elif self.stadio_capacita >= 25000:
                return 2.5  # Stadio medio
            elif self.stadio_capacita >= 15000:
                return 2.0  # Stadio piccolo
            else:
                return 1.5  # Stadio molto piccolo (Monza)

        elif self.campionato == League.SERIE_B:
            # Range Serie B: 5K-58K
            if self.stadio_capacita >= 50000:
                return 3.5  # San Nicola (Bari)
            elif self.stadio_capacita >= 30000:
                return 3.0  # Barbera (Palermo), Ferraris (Sampdoria)
            elif self.stadio_capacita >= 20000:
                return 2.5  # Tardini (Parma)
            elif self.stadio_capacita >= 10000:
                return 2.0  # Stadio medio
            else:
                return 1.5  # Stadio piccolo (Südtirol)

        elif self.campionato == League.SERIE_C:
            # Range Serie C: 1.5K-38K
            if self.stadio_capacita >= 25000:
                return 3.0  # Partenio (Avellino), Massimino (Catania)
            elif self.stadio_capacita >= 15000:
                return 2.5  # Stadio grande per Serie C
            elif self.stadio_capacita >= 7000:
                return 2.0  # Stadio medio
            elif self.stadio_capacita >= 3000:
                return 1.5  # Stadio piccolo
            else:
                return 1.0  # Stadio molto piccolo

        else:  # Europa
            if self.stadio_capacita >= 70000:
                return 4.0
            elif self.stadio_capacita >= 50000:
                return 3.5
            elif self.stadio_capacita >= 30000:
                return 3.0
            else:
                return 2.0

    def _get_technical_score(self) -> float:
        """Calcola il punteggio tecnico basato sulle statistiche dei CSV"""
        # Media delle statistiche tecniche
        avg_stats = (self.attacco + self.difesa + self.fisico + self.velocita) / 4

        # Normalizza in base al campionato
        if self.campionato == League.SERIE_A:
            # Range Serie A: 72-86 (dai dati reali)
            if avg_stats >= 83:
                return 3.0  # Top (Inter, Milan, Juventus, Napoli)
            elif avg_stats >= 80:
                return 2.5  # Buone (Atalanta, Roma, Lazio)
            elif avg_stats >= 77:
                return 2.0  # Medie (Bologna, Torino)
            else:
                return 1.5  # Salvezza (Frosinone, Salernitana)

        elif self.campionato == League.SERIE_B:
            # Range Serie B: 69-77 (dai dati reali)
            if avg_stats >= 75:
                return 2.5  # Top Serie B (Parma, Sampdoria)
            elif avg_stats >= 73:
                return 2.0  # Playoff
            elif avg_stats >= 71:
                return 1.5  # Metà classifica
            else:
                return 1.0  # Salvezza

        elif self.campionato == League.SERIE_C:
            # Range Serie C: 62-74 (dai dati reali)
            if avg_stats >= 71:
                return 2.0  # Top Serie C (Cesena, Benevento)
            elif avg_stats >= 68:
                return 1.5  # Playoff
            elif avg_stats >= 65:
                return 1.0  # Metà classifica
            else:
                return 0.5  # Salvezza

        else:  # Europa
            # Assume range simile o superiore alla Serie A
            if avg_stats >= 85:
                return 3.0
            elif avg_stats >= 80:
                return 2.5
            elif avg_stats >= 75:
                return 2.0
            else:
                return 1.5



    def _get_european_objective(self, score: float) -> str:
        """Determina l'obiettivo per squadre europee"""
        if score >= 7.0:
            return "Vincere Champions League"
        elif score >= 5.5:
            return "Qualificazione Champions League"
        elif score >= 4.0:
            return "Qualificazione Europa League"
        elif score >= 2.5:
            return "Metà classifica alta"
        elif score >= 1.5:
            return "Evitare retrocessione"
        else:
            return "Mantenere la categoria"

    def _get_serie_a_objective(self, score: float) -> str:
        """Determina l'obiettivo per Serie A basato sui dati reali"""
        if score >= 9.0:
            return "Vincere lo Scudetto"  # Solo i top assoluti (Inter, Milan, Juventus con tutto al massimo)
        elif score >= 7.5:
            return "Qualificazione Champions League"  # Top club con buoni parametri
        elif score >= 6.0:
            return "Qualificazione Europa League"  # Club buoni
        elif score >= 4.5:
            return "Posizionamento a metà classifica"  # Club medi
        elif score >= 3.0:
            return "Evitare la retrocessione"  # Club in difficoltà
        else:
            return "Lottare per la salvezza"  # Club con gravi problemi

    def _get_serie_b_objective(self, score: float) -> str:
        """Determina l'obiettivo per Serie B basato sui dati reali"""
        if score >= 6.5:
            return "Promozione diretta in Serie A"  # Top assoluti (Parma, Sampdoria con tutto al massimo)
        elif score >= 5.0:
            return "Raggiungere i playoff promozione"  # Club forti
        elif score >= 3.5:
            return "Posizionamento a metà classifica"  # Club medi
        elif score >= 2.5:
            return "Mantenere la categoria"  # Club stabili
        else:
            return "Evitare la retrocessione"  # Club in difficoltà

    def _get_serie_c_objective(self, score: float) -> str:
        """Determina l'obiettivo per Serie C basato sui dati reali"""
        if score >= 5.0:
            return "Promozione diretta in Serie B"  # Top assoluti (Cesena, Catania con tutto al massimo)
        elif score >= 3.5:
            return "Raggiungere i playoff promozione"  # Club forti
        elif score >= 2.5:
            return "Posizionamento a metà classifica"  # Club medi
        elif score >= 1.5:
            return "Mantenere la categoria"  # Club stabili
        else:
            return "Evitare la retrocessione"  # Club in difficoltà
    
    def _get_secondary_objectives(self) -> List[str]:
        """Determina gli obiettivi secondari basati su multiple variabili"""
        objectives_pool = []

        # Obiettivi basati sul budget
        if self.budget_mln >= 100:
            objectives_pool.extend([
                "Migliorare la reputazione internazionale",
                "Raggiungere la finale di Coppa Italia",
                "Sviluppare il centro sportivo"
            ])
        elif self.budget_mln >= 50:
            objectives_pool.extend([
                "Raggiungere i quarti di Coppa Italia",
                "Valorizzare giovani promettenti",
                "Migliorare le infrastrutture"
            ])
        elif self.budget_mln >= 20:
            objectives_pool.extend([
                "Buon piazzamento in Coppa Italia",
                "Aumentare l'affluenza allo stadio",
                "Pareggio di bilancio"
            ])
        else:
            objectives_pool.extend([
                "Ridurre il monte ingaggi",
                "Valorizzare settore giovanile",
                "Migliorare la sostenibilità economica"
            ])

        # Obiettivi basati sulla capacità dello stadio
        if self.stadio_capacita >= 40000:
            objectives_pool.append("Mantenere alta l'affluenza allo stadio")
        elif self.stadio_capacita <= 15000:
            objectives_pool.append("Ampliare lo stadio")

        # Obiettivi basati sulla tradizione
        tradition_score = self._get_tradition_score()
        if tradition_score >= 3.5:
            objectives_pool.append("Mantenere alta la reputazione storica")
        elif tradition_score <= 1.5:
            objectives_pool.append("Costruire una nuova identità")

        # Obiettivi specifici per campionato
        if self.campionato == League.EUROPA:
            objectives_pool.extend([
                "Competere a livello internazionale",
                "Attrarre talenti globali"
            ])
        elif self.campionato == League.SERIE_C:
            objectives_pool.extend([
                "Sviluppare giovani locali",
                "Mantenere legame con il territorio"
            ])

        # Seleziona 2-3 obiettivi casuali
        unique_objectives = list(set(objectives_pool))
        num_objectives = random.randint(2, min(3, len(unique_objectives)))
        return random.sample(unique_objectives, num_objectives)
    
    def add_player(self, player: Player):
        """Aggiunge un giocatore alla rosa"""
        self.giocatori.append(player)
        self.uscite_mensili += player.stipendio
    
    def remove_player(self, player: Player):
        """Rimuove un giocatore dalla rosa"""
        if player in self.giocatori:
            self.giocatori.remove(player)
            self.uscite_mensili -= player.stipendio
    
    def add_staff(self, staff_member: StaffMember):
        """Aggiunge un membro dello staff"""
        # Rimuovi eventuale staff dello stesso ruolo
        self.staff = [s for s in self.staff if s.ruolo != staff_member.ruolo]
        self.staff.append(staff_member)
        self.uscite_mensili += staff_member.stipendio
    
    def remove_staff(self, staff_member: StaffMember):
        """Rimuove un membro dello staff"""
        if staff_member in self.staff:
            self.staff.remove(staff_member)
            self.uscite_mensili -= staff_member.stipendio
    
    def get_players_by_position(self, position: Position) -> List[Player]:
        """Restituisce i giocatori per posizione"""
        return [p for p in self.giocatori if p.posizione == position]
    
    def get_available_players(self) -> List[Player]:
        """Restituisce i giocatori disponibili"""
        return [p for p in self.giocatori if p.is_available()]
    
    def get_staff_by_role(self, role: StaffRole) -> Optional[StaffMember]:
        """Restituisce un membro dello staff per ruolo"""
        for staff_member in self.staff:
            if staff_member.ruolo == role:
                return staff_member
        return None
    
    def get_team_overall(self) -> int:
        """Calcola l'overall medio della squadra"""
        if not self.giocatori:
            return 50
        
        total_overall = sum(player.stats.overall() for player in self.giocatori)
        return int(total_overall / len(self.giocatori))
    
    def get_monthly_balance(self) -> int:
        """Calcola il bilancio mensile"""
        return self.entrate_mensili - self.uscite_mensili
    
    def can_afford_player(self, player_cost: int, player_salary: int) -> bool:
        """Verifica se il club può permettersi un giocatore"""
        # Verifica budget trasferimenti
        can_afford_transfer = player_cost <= self.budget_trasferimenti

        # Verifica budget stipendi
        remaining_salary_budget = self.budget_stipendi - self.uscite_mensili
        can_afford_salary = player_salary <= remaining_salary_budget

        return can_afford_transfer and can_afford_salary

    def get_budget_breakdown(self) -> dict:
        """Restituisce la suddivisione dettagliata del budget"""
        return {
            'budget_totale': self.budget,
            'budget_mln': self.budget_mln,
            'budget_trasferimenti': self.budget_trasferimenti,
            'budget_stipendi_mensile': self.budget_stipendi,
            'budget_stipendi_annuale': self.budget_stipendi * 12,
            'budget_rimanente': self.budget - self.budget_trasferimenti - (self.budget_stipendi * 12),
            'stipendi_utilizzati': self.uscite_mensili,
            'stipendi_disponibili': self.budget_stipendi - self.uscite_mensili,
            'percentuale_stipendi_usata': (self.uscite_mensili / self.budget_stipendi * 100) if self.budget_stipendi > 0 else 0
        }

    def get_club_tier(self) -> str:
        """Restituisce il livello del club basato sul budget"""
        if self.campionato == League.EUROPA:
            if self.budget_mln >= 500:
                return "World Class"
            elif self.budget_mln >= 200:
                return "Elite"
            elif self.budget_mln >= 100:
                return "Top"
            elif self.budget_mln >= 50:
                return "Good"
            else:
                return "Average"
        else:
            if self.budget_mln >= 200:
                return "Top"
            elif self.budget_mln >= 100:
                return "Elite"
            elif self.budget_mln >= 50:
                return "Good"
            elif self.budget_mln >= 25:
                return "Average"
            else:
                return "Small"
    
    def update_weekly(self):
        """Aggiorna lo stato del club settimanalmente"""
        # Aggiorna giocatori e staff
        for player in self.giocatori:
            player.update_weekly()
        
        for staff_member in self.staff:
            staff_member.update_weekly()
        
        # Aggiorna soddisfazione tifosi
        self._update_fan_satisfaction()
        
        # Possibili eventi casuali
        self._random_events()
    
    def _update_fan_satisfaction(self):
        """Aggiorna la soddisfazione dei tifosi"""
        # Basato su risultati recenti, obiettivi, e gestione
        if self.partite_giocate > 0:
            win_rate = self.vittorie / self.partite_giocate
            if win_rate > 0.6:
                self.tifosi_soddisfazione = min(100, self.tifosi_soddisfazione + 2)
            elif win_rate < 0.3:
                self.tifosi_soddisfazione = max(0, self.tifosi_soddisfazione - 2)
    
    def _random_events(self):
        """Eventi casuali che possono accadere al club"""
        if random.random() < 0.05:  # 5% di possibilità a settimana
            events = [
                "sponsor_interest",
                "fan_protest",
                "media_attention",
                "infrastructure_issue"
            ]
            event = random.choice(events)
            # Qui si potrebbero implementare gli effetti degli eventi
    
    def get_formation_players(self, formation: str = "4-4-2") -> Dict[str, List[Player]]:
        """Restituisce i giocatori per una formazione specifica"""
        available_players = self.get_available_players()
        
        formation_dict = {
            "portieri": [p for p in available_players if p.posizione == Position.PORTIERE][:1],
            "difensori": [p for p in available_players if p.posizione == Position.DIFENSORE][:4],
            "centrocampisti": [p for p in available_players if p.posizione == Position.CENTROCAMPISTA][:4],
            "attaccanti": [p for p in available_players if p.posizione == Position.ATTACCANTE][:2]
        }
        
        return formation_dict
    
    def __str__(self):
        return f"{self.nome} ({self.campionato.value}) - Budget: €{self.budget:,}"
