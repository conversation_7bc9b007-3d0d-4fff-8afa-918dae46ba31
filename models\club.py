"""
Modello per rappresentare un club di calcio
"""

from typing import List, Dict, Optional
from enum import Enum
import random

from .player import Player, Position
from .staff import StaffMember, StaffRole

class League(Enum):
    """Campionati"""
    SERIE_A = "Serie A"
    SERIE_B = "Serie B"
    SERIE_C = "Serie C"
    EUROPA = "Europa"  # Per squadre straniere

class ClubStatus(Enum):
    """Stato del club"""
    ATTIVO = "attivo"
    FALLIMENTO = "fallimento"
    VENDITA = "in_vendita"

class Club:
    """Classe che rappresenta un club di calcio"""
    
    def __init__(self,
                 club_id: int,
                 nome: str,
                 citta: str,
                 campionato: League,
                 budget: int = 1000000,
                 stadio_nome: str = "",
                 stadio_capacita: int = 10000,
                 budget_mln: float = None):
        
        self.id = club_id
        self.nome = nome
        self.citta = citta
        self.campionato = campionato
        self.budget = budget
        self.stadio_nome = stadio_nome or f"Stadio {nome}"
        self.stadio_capacita = stadio_capacita

        # Budget dettagliato
        self.budget_mln = budget_mln or (budget / 1000000)  # Converti in milioni se non fornito
        self.budget_trasferimenti = self._calculate_transfer_budget()
        self.budget_stipendi = self._calculate_salary_budget()
        
        # Rosa e staff
        self.giocatori: List[Player] = []
        self.staff: List[StaffMember] = []
        
        # Finanze
        self.debiti = 0
        self.entrate_mensili = self._calculate_base_income()
        self.uscite_mensili = 0
        
        # Infrastrutture
        self.centro_sportivo_livello = 1
        self.settore_giovanile_livello = 1
        self.centro_medico_livello = 1
        
        # Statistiche stagionali
        self.punti = 0
        self.partite_giocate = 0
        self.vittorie = 0
        self.pareggi = 0
        self.sconfitte = 0
        self.gol_fatti = 0
        self.gol_subiti = 0
        
        # Obiettivi stagionali basati sul budget
        self.obiettivo_principale = self._get_season_objective_by_budget()
        self.obiettivi_secondari = self._get_secondary_objectives()
        
        # Tifosi e reputazione
        self.tifosi_soddisfazione = random.randint(60, 80)
        self.reputazione = self._calculate_base_reputation()
        
        # Sponsor
        self.sponsor_principale = None
        self.sponsor_tecnico = None
        self.altri_sponsor = []
        
        self.status = ClubStatus.ATTIVO

    def _calculate_transfer_budget(self) -> int:
        """Calcola il budget disponibile per i trasferimenti"""
        # Percentuale del budget totale dedicata ai trasferimenti
        if self.campionato == League.EUROPA:
            transfer_percentage = 0.4  # 40% per squadre europee
        elif self.campionato == League.SERIE_A:
            transfer_percentage = 0.35  # 35% per Serie A
        elif self.campionato == League.SERIE_B:
            transfer_percentage = 0.3   # 30% per Serie B
        else:  # Serie C
            transfer_percentage = 0.25  # 25% per Serie C

        return int(self.budget * transfer_percentage)

    def _calculate_salary_budget(self) -> int:
        """Calcola il budget mensile per gli stipendi"""
        # Percentuale del budget totale dedicata agli stipendi (annuale)
        if self.campionato == League.EUROPA:
            salary_percentage = 0.5   # 50% per squadre europee
        elif self.campionato == League.SERIE_A:
            salary_percentage = 0.45  # 45% per Serie A
        elif self.campionato == League.SERIE_B:
            salary_percentage = 0.4   # 40% per Serie B
        else:  # Serie C
            salary_percentage = 0.35  # 35% per Serie C

        annual_salary_budget = int(self.budget * salary_percentage)
        return annual_salary_budget // 12  # Budget mensile
    
    def _calculate_base_income(self) -> int:
        """Calcola le entrate base mensili"""
        base_income = {
            League.SERIE_A: 2000000,
            League.SERIE_B: 500000,
            League.SERIE_C: 100000,
            League.EUROPA: int(self.budget * 0.05)  # 5% del budget mensile per squadre europee
        }
        return base_income.get(self.campionato, 50000)
    
    def _calculate_base_reputation(self) -> int:
        """Calcola la reputazione base"""
        if self.campionato == League.EUROPA:
            # Reputazione basata sul budget per squadre europee
            if self.budget > 500000000:
                return random.randint(95, 99)  # World class
            elif self.budget > 200000000:
                return random.randint(85, 95)  # Elite
            elif self.budget > 100000000:
                return random.randint(75, 85)  # Top
            else:
                return random.randint(60, 75)  # Good
        else:
            base_reputation = {
                League.SERIE_A: random.randint(70, 90),
                League.SERIE_B: random.randint(50, 70),
                League.SERIE_C: random.randint(30, 50)
            }
            return base_reputation.get(self.campionato, 30)
    
    def _get_season_objective_by_budget(self) -> str:
        """Determina l'obiettivo principale basato su multiple variabili"""
        # Calcola un punteggio complessivo considerando vari fattori
        objective_score = self._calculate_objective_score()

        if self.campionato == League.EUROPA:
            return self._get_european_objective(objective_score)
        elif self.campionato == League.SERIE_A:
            return self._get_serie_a_objective(objective_score)
        elif self.campionato == League.SERIE_B:
            return self._get_serie_b_objective(objective_score)
        else:  # Serie C
            return self._get_serie_c_objective(objective_score)

    def _calculate_objective_score(self) -> float:
        """Calcola un punteggio per determinare l'obiettivo basato su multiple variabili"""
        score = 0.0

        # 1. Fattore Budget (peso 35%)
        budget_score = min(self.budget_mln / 50, 8.0)  # Normalizza diversamente
        score += budget_score * 0.35

        # 2. Fattore Stadio (peso 20%)
        stadium_score = self._get_stadium_score()
        score += stadium_score * 0.20

        # 3. Fattore Storico/Tradizione (peso 25%)
        tradition_score = self._get_tradition_score()
        score += tradition_score * 0.25

        # 4. Fattore Casuale/Ambizione (peso 20% - aumentato per più variabilità)
        ambition_score = random.uniform(-1, 4)  # Può anche essere negativo
        score += ambition_score * 0.20

        return max(0, score)  # Non può essere negativo

    def _get_stadium_score(self) -> float:
        """Calcola il punteggio basato sullo stadio"""
        # Capacità stadio influenza l'ambizione del club
        if self.stadio_capacita >= 70000:
            return 4.0  # Stadi molto grandi = grandi ambizioni
        elif self.stadio_capacita >= 50000:
            return 3.5
        elif self.stadio_capacita >= 35000:
            return 3.0
        elif self.stadio_capacita >= 25000:
            return 2.5
        elif self.stadio_capacita >= 15000:
            return 2.0
        elif self.stadio_capacita >= 10000:
            return 1.5
        else:
            return 1.0  # Stadi piccoli = obiettivi modesti

    def _get_tradition_score(self) -> float:
        """Calcola il punteggio basato sulla tradizione del club"""
        # Club storici hanno maggiori ambizioni
        traditional_clubs = {
            # Serie A - Club storici
            "Juventus": 4.0, "Inter": 4.0, "Milan": 4.0, "Roma": 3.5, "Lazio": 3.5,
            "Napoli": 3.5, "Fiorentina": 3.0, "Torino": 3.0, "Genoa": 2.5, "Bologna": 2.5,
            "Atalanta": 2.0, "Sampdoria": 2.5, "Cagliari": 2.0, "Udinese": 2.0,

            # Serie B - Club con storia
            "Parma": 3.0, "Palermo": 2.5, "Bari": 2.5, "Brescia": 2.0, "Venezia": 2.0,
            "Cremonese": 2.0, "Modena": 2.0, "Como": 2.0, "Pisa": 2.0, "Reggina": 2.0,

            # Europa - Giganti storici
            "Real Madrid": 5.0, "FC Barcelona": 5.0, "Bayern München": 5.0,
            "Manchester United": 4.5, "Liverpool FC": 4.5, "Arsenal FC": 4.5,
            "Manchester City": 3.5, "Chelsea FC": 3.5, "Tottenham Hotspur": 3.0,
            "Borussia Dortmund": 4.0, "Ajax": 4.0, "AC Milan": 4.5,
            "Paris Saint-Germain": 3.0, "Atlético Madrid": 3.5,
            "FC Porto": 3.5, "SL Benfica": 3.5, "Sporting CP": 3.0,
            "Olympique Lyonnais": 3.0, "Olympique de Marseille": 3.0
        }

        return traditional_clubs.get(self.nome, 1.0)  # Default per club senza storia particolare

    def _get_european_objective(self, score: float) -> str:
        """Determina l'obiettivo per squadre europee"""
        if score >= 7.0:
            return "Vincere Champions League"
        elif score >= 5.5:
            return "Qualificazione Champions League"
        elif score >= 4.0:
            return "Qualificazione Europa League"
        elif score >= 2.5:
            return "Metà classifica alta"
        elif score >= 1.5:
            return "Evitare retrocessione"
        else:
            return "Mantenere la categoria"

    def _get_serie_a_objective(self, score: float) -> str:
        """Determina l'obiettivo per Serie A"""
        if score >= 6.5:
            return "Vincere lo Scudetto"
        elif score >= 5.0:
            return "Qualificazione Champions League"
        elif score >= 3.5:
            return "Qualificazione Europa League"
        elif score >= 2.5:
            return "Posizionamento a metà classifica"
        elif score >= 1.5:
            return "Evitare la retrocessione"
        else:
            return "Lottare per la salvezza"

    def _get_serie_b_objective(self, score: float) -> str:
        """Determina l'obiettivo per Serie B"""
        if score >= 5.5:
            return "Promozione diretta in Serie A"
        elif score >= 4.0:
            return "Raggiungere i playoff promozione"
        elif score >= 2.5:
            return "Posizionamento a metà classifica"
        elif score >= 1.5:
            return "Mantenere la categoria"
        else:
            return "Evitare la retrocessione"

    def _get_serie_c_objective(self, score: float) -> str:
        """Determina l'obiettivo per Serie C"""
        if score >= 4.0:
            return "Promozione diretta in Serie B"
        elif score >= 3.0:
            return "Raggiungere i playoff promozione"
        elif score >= 2.0:
            return "Posizionamento a metà classifica"
        elif score >= 1.0:
            return "Mantenere la categoria"
        else:
            return "Evitare la retrocessione"
    
    def _get_secondary_objectives(self) -> List[str]:
        """Determina gli obiettivi secondari basati su multiple variabili"""
        objectives_pool = []

        # Obiettivi basati sul budget
        if self.budget_mln >= 100:
            objectives_pool.extend([
                "Migliorare la reputazione internazionale",
                "Raggiungere la finale di Coppa Italia",
                "Sviluppare il centro sportivo"
            ])
        elif self.budget_mln >= 50:
            objectives_pool.extend([
                "Raggiungere i quarti di Coppa Italia",
                "Valorizzare giovani promettenti",
                "Migliorare le infrastrutture"
            ])
        elif self.budget_mln >= 20:
            objectives_pool.extend([
                "Buon piazzamento in Coppa Italia",
                "Aumentare l'affluenza allo stadio",
                "Pareggio di bilancio"
            ])
        else:
            objectives_pool.extend([
                "Ridurre il monte ingaggi",
                "Valorizzare settore giovanile",
                "Migliorare la sostenibilità economica"
            ])

        # Obiettivi basati sulla capacità dello stadio
        if self.stadio_capacita >= 40000:
            objectives_pool.append("Mantenere alta l'affluenza allo stadio")
        elif self.stadio_capacita <= 15000:
            objectives_pool.append("Ampliare lo stadio")

        # Obiettivi basati sulla tradizione
        tradition_score = self._get_tradition_score()
        if tradition_score >= 3.5:
            objectives_pool.append("Mantenere alta la reputazione storica")
        elif tradition_score <= 1.5:
            objectives_pool.append("Costruire una nuova identità")

        # Obiettivi specifici per campionato
        if self.campionato == League.EUROPA:
            objectives_pool.extend([
                "Competere a livello internazionale",
                "Attrarre talenti globali"
            ])
        elif self.campionato == League.SERIE_C:
            objectives_pool.extend([
                "Sviluppare giovani locali",
                "Mantenere legame con il territorio"
            ])

        # Seleziona 2-3 obiettivi casuali
        unique_objectives = list(set(objectives_pool))
        num_objectives = random.randint(2, min(3, len(unique_objectives)))
        return random.sample(unique_objectives, num_objectives)
    
    def add_player(self, player: Player):
        """Aggiunge un giocatore alla rosa"""
        self.giocatori.append(player)
        self.uscite_mensili += player.stipendio
    
    def remove_player(self, player: Player):
        """Rimuove un giocatore dalla rosa"""
        if player in self.giocatori:
            self.giocatori.remove(player)
            self.uscite_mensili -= player.stipendio
    
    def add_staff(self, staff_member: StaffMember):
        """Aggiunge un membro dello staff"""
        # Rimuovi eventuale staff dello stesso ruolo
        self.staff = [s for s in self.staff if s.ruolo != staff_member.ruolo]
        self.staff.append(staff_member)
        self.uscite_mensili += staff_member.stipendio
    
    def remove_staff(self, staff_member: StaffMember):
        """Rimuove un membro dello staff"""
        if staff_member in self.staff:
            self.staff.remove(staff_member)
            self.uscite_mensili -= staff_member.stipendio
    
    def get_players_by_position(self, position: Position) -> List[Player]:
        """Restituisce i giocatori per posizione"""
        return [p for p in self.giocatori if p.posizione == position]
    
    def get_available_players(self) -> List[Player]:
        """Restituisce i giocatori disponibili"""
        return [p for p in self.giocatori if p.is_available()]
    
    def get_staff_by_role(self, role: StaffRole) -> Optional[StaffMember]:
        """Restituisce un membro dello staff per ruolo"""
        for staff_member in self.staff:
            if staff_member.ruolo == role:
                return staff_member
        return None
    
    def get_team_overall(self) -> int:
        """Calcola l'overall medio della squadra"""
        if not self.giocatori:
            return 50
        
        total_overall = sum(player.stats.overall() for player in self.giocatori)
        return int(total_overall / len(self.giocatori))
    
    def get_monthly_balance(self) -> int:
        """Calcola il bilancio mensile"""
        return self.entrate_mensili - self.uscite_mensili
    
    def can_afford_player(self, player_cost: int, player_salary: int) -> bool:
        """Verifica se il club può permettersi un giocatore"""
        # Verifica budget trasferimenti
        can_afford_transfer = player_cost <= self.budget_trasferimenti

        # Verifica budget stipendi
        remaining_salary_budget = self.budget_stipendi - self.uscite_mensili
        can_afford_salary = player_salary <= remaining_salary_budget

        return can_afford_transfer and can_afford_salary

    def get_budget_breakdown(self) -> dict:
        """Restituisce la suddivisione dettagliata del budget"""
        return {
            'budget_totale': self.budget,
            'budget_mln': self.budget_mln,
            'budget_trasferimenti': self.budget_trasferimenti,
            'budget_stipendi_mensile': self.budget_stipendi,
            'budget_stipendi_annuale': self.budget_stipendi * 12,
            'budget_rimanente': self.budget - self.budget_trasferimenti - (self.budget_stipendi * 12),
            'stipendi_utilizzati': self.uscite_mensili,
            'stipendi_disponibili': self.budget_stipendi - self.uscite_mensili,
            'percentuale_stipendi_usata': (self.uscite_mensili / self.budget_stipendi * 100) if self.budget_stipendi > 0 else 0
        }

    def get_club_tier(self) -> str:
        """Restituisce il livello del club basato sul budget"""
        if self.campionato == League.EUROPA:
            if self.budget_mln >= 500:
                return "World Class"
            elif self.budget_mln >= 200:
                return "Elite"
            elif self.budget_mln >= 100:
                return "Top"
            elif self.budget_mln >= 50:
                return "Good"
            else:
                return "Average"
        else:
            if self.budget_mln >= 200:
                return "Top"
            elif self.budget_mln >= 100:
                return "Elite"
            elif self.budget_mln >= 50:
                return "Good"
            elif self.budget_mln >= 25:
                return "Average"
            else:
                return "Small"
    
    def update_weekly(self):
        """Aggiorna lo stato del club settimanalmente"""
        # Aggiorna giocatori e staff
        for player in self.giocatori:
            player.update_weekly()
        
        for staff_member in self.staff:
            staff_member.update_weekly()
        
        # Aggiorna soddisfazione tifosi
        self._update_fan_satisfaction()
        
        # Possibili eventi casuali
        self._random_events()
    
    def _update_fan_satisfaction(self):
        """Aggiorna la soddisfazione dei tifosi"""
        # Basato su risultati recenti, obiettivi, e gestione
        if self.partite_giocate > 0:
            win_rate = self.vittorie / self.partite_giocate
            if win_rate > 0.6:
                self.tifosi_soddisfazione = min(100, self.tifosi_soddisfazione + 2)
            elif win_rate < 0.3:
                self.tifosi_soddisfazione = max(0, self.tifosi_soddisfazione - 2)
    
    def _random_events(self):
        """Eventi casuali che possono accadere al club"""
        if random.random() < 0.05:  # 5% di possibilità a settimana
            events = [
                "sponsor_interest",
                "fan_protest",
                "media_attention",
                "infrastructure_issue"
            ]
            event = random.choice(events)
            # Qui si potrebbero implementare gli effetti degli eventi
    
    def get_formation_players(self, formation: str = "4-4-2") -> Dict[str, List[Player]]:
        """Restituisce i giocatori per una formazione specifica"""
        available_players = self.get_available_players()
        
        formation_dict = {
            "portieri": [p for p in available_players if p.posizione == Position.PORTIERE][:1],
            "difensori": [p for p in available_players if p.posizione == Position.DIFENSORE][:4],
            "centrocampisti": [p for p in available_players if p.posizione == Position.CENTROCAMPISTA][:4],
            "attaccanti": [p for p in available_players if p.posizione == Position.ATTACCANTE][:2]
        }
        
        return formation_dict
    
    def __str__(self):
        return f"{self.nome} ({self.campionato.value}) - Budget: €{self.budget:,}"
