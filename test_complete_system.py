#!/usr/bin/env python3
"""
Test completo del sistema con squadre italiane ed europee
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.club import Club, League
from models.player import Position
from core.player_generator import player_generator
from utils.json_loader import data_loader

def test_complete_system():
    """Test completo del sistema"""
    print("🚀 Football President - Test Sistema Completo")
    print("=" * 70)
    
    # Simula il caricamento completo come fa l'interfaccia
    leagues_data = {
        "Serie A": ("serie_a.csv", League.SERIE_A),
        "Serie B": ("serie_b.csv", League.SERIE_B),
        "Serie C Girone A": ("serie_c_girone_A.csv", League.SERIE_C),
        "Serie C Girone B": ("serie_c_girone_B.csv", League.SERIE_C),
        "Serie C Girone C": ("serie_c_girone_C.csv", League.SERIE_C),
        "Squadre Europee": ("altre_europa.csv", League.EUROPA)
    }
    
    all_clubs = []
    total_players = 0
    league_stats = {}
    
    print("📊 Caricamento completo del database...")
    
    for league_name, (filename, league_enum) in leagues_data.items():
        print(f"\n🔄 {league_name}:")
        
        # Carica dati dal CSV
        teams_data = data_loader.load_csv(filename)
        
        if teams_data:
            clubs = []
            for i, team_data in enumerate(teams_data):
                try:
                    # Determina budget
                    if league_enum == League.EUROPA:
                        budget_mln = float(team_data.get('budget_mln', '50'))
                        budget = int(budget_mln * 1000000)
                    else:
                        base_budgets = {
                            League.SERIE_A: 30000000,
                            League.SERIE_B: 8000000,
                            League.SERIE_C: 2000000
                        }
                        base_budget = base_budgets.get(league_enum, 1000000)
                        import random
                        budget_variation = base_budget * 0.3
                        budget = int(base_budget + random.uniform(-budget_variation, budget_variation))
                    
                    # Capacità stadio
                    if league_enum == League.EUROPA:
                        capacita = int(team_data.get('capienza_stadio', '30000').replace(',', '').replace('.', ''))
                    else:
                        capacita = int(str(team_data.get('capienza_stadio', team_data.get('capacita', '10000'))).replace(',', '').replace('.', ''))
                    
                    if capacita == 0:
                        default_capacities = {
                            League.SERIE_A: 25000,
                            League.SERIE_B: 15000,
                            League.SERIE_C: 8000,
                            League.EUROPA: 40000
                        }
                        capacita = default_capacities.get(league_enum, 5000)
                    
                    nome_club = team_data.get('squadra', team_data.get('nome', 'Club Sconosciuto')).strip()
                    
                    club = Club(
                        club_id=len(all_clubs) + 1,
                        nome=nome_club,
                        citta=team_data.get('citta', nome_club).strip(),
                        campionato=league_enum,
                        budget=budget,
                        stadio_nome=team_data.get('stadio', f"Stadio {nome_club}").strip(),
                        stadio_capacita=capacita
                    )
                    
                    # Genera la rosa automaticamente
                    squad = player_generator.generate_squad(club)
                    for player in squad:
                        club.add_player(player)
                    
                    clubs.append(club)
                    total_players += len(squad)
                    
                except Exception as e:
                    print(f"   ❌ Errore con {team_data}: {e}")
            
            all_clubs.extend(clubs)
            league_stats[league_name] = {
                'clubs': len(clubs),
                'players': len(clubs) * 25,
                'avg_overall': sum(c.get_team_overall() for c in clubs) / len(clubs) if clubs else 0,
                'avg_budget': sum(c.budget for c in clubs) / len(clubs) if clubs else 0
            }
            
            print(f"   ✅ {len(clubs)} squadre caricate")
    
    print("\n" + "=" * 70)
    print("📈 STATISTICHE SISTEMA COMPLETO")
    print("=" * 70)
    
    print(f"🏟️  Squadre totali: {len(all_clubs)}")
    print(f"⚽ Giocatori totali: {total_players}")
    print(f"📊 Media giocatori per squadra: {total_players / len(all_clubs):.1f}")
    
    # Statistiche per campionato
    print(f"\n📋 DETTAGLI PER CAMPIONATO:")
    for league_name, stats in league_stats.items():
        print(f"   {league_name}:")
        print(f"     • Squadre: {stats['clubs']}")
        print(f"     • Giocatori: {stats['players']}")
        print(f"     • Overall medio: {stats['avg_overall']:.1f}")
        print(f"     • Budget medio: €{stats['avg_budget']:,.0f}")
    
    # Top 10 squadre per budget
    print(f"\n💰 TOP 10 SQUADRE PER BUDGET:")
    top_budget = sorted(all_clubs, key=lambda c: c.budget, reverse=True)[:10]
    for i, club in enumerate(top_budget, 1):
        print(f"   {i:2d}. {club.nome:<25} ({club.campionato.value:<8}) - €{club.budget:>12,}")
    
    # Top 10 squadre per overall
    print(f"\n🏆 TOP 10 SQUADRE PER OVERALL:")
    top_overall = sorted(all_clubs, key=lambda c: c.get_team_overall(), reverse=True)[:10]
    for i, club in enumerate(top_overall, 1):
        print(f"   {i:2d}. {club.nome:<25} ({club.campionato.value:<8}) - Overall: {club.get_team_overall()}")
    
    # Analisi distribuzione nazionalità
    print(f"\n🌍 DISTRIBUZIONE NAZIONALITÀ (Top 15):")
    nationality_count = {}
    for club in all_clubs:
        for player in club.giocatori:
            nationality_count[player.nazionalita] = nationality_count.get(player.nazionalita, 0) + 1
    
    top_nationalities = sorted(nationality_count.items(), key=lambda x: x[1], reverse=True)[:15]
    for nationality, count in top_nationalities:
        percentage = (count / total_players) * 100
        print(f"   {nationality:<15}: {count:>4} giocatori ({percentage:>5.1f}%)")
    
    # Analisi valori di mercato
    print(f"\n💎 ANALISI VALORI DI MERCATO:")
    all_players = []
    for club in all_clubs:
        for player in club.giocatori:
            all_players.append((player, club))
    
    total_market_value = sum(p.valore_mercato for p, _ in all_players)
    total_salaries = sum(p.stipendio for p, _ in all_players)
    
    print(f"   Valore totale mercato: €{total_market_value:,}")
    print(f"   Stipendi totali mensili: €{total_salaries:,}")
    print(f"   Valore medio giocatore: €{total_market_value // len(all_players):,}")
    
    # Top 10 giocatori più costosi
    print(f"\n💰 TOP 10 GIOCATORI PIÙ COSTOSI:")
    top_valuable = sorted(all_players, key=lambda x: x[0].valore_mercato, reverse=True)[:10]
    for i, (player, club) in enumerate(top_valuable, 1):
        print(f"   {i:2d}. {player.nome_completo():<20} ({club.nome:<20}) - €{player.valore_mercato:>10,}")
    
    # Verifica integrità del sistema
    print(f"\n🔍 VERIFICA INTEGRITÀ SISTEMA:")
    
    # Verifica che tutte le squadre abbiano giocatori
    clubs_without_players = [c for c in all_clubs if len(c.giocatori) == 0]
    if clubs_without_players:
        print(f"   ❌ {len(clubs_without_players)} squadre senza giocatori!")
    else:
        print(f"   ✅ Tutte le squadre hanno una rosa completa")
    
    # Verifica distribuzione posizioni
    total_by_position = {pos: 0 for pos in Position}
    for club in all_clubs:
        for player in club.giocatori:
            total_by_position[player.posizione] += 1
    
    print(f"   📊 Distribuzione posizioni:")
    for position, count in total_by_position.items():
        expected = len(all_clubs) * {"P": 3, "D": 8, "C": 8, "A": 6}[position.value]
        print(f"     • {position.value}: {count} (attesi: {expected})")
    
    # Verifica range budget
    min_budget = min(c.budget for c in all_clubs)
    max_budget = max(c.budget for c in all_clubs)
    print(f"   💰 Range budget: €{min_budget:,} - €{max_budget:,}")
    
    # Verifica range overall
    min_overall = min(c.get_team_overall() for c in all_clubs)
    max_overall = max(c.get_team_overall() for c in all_clubs)
    print(f"   📈 Range overall: {min_overall} - {max_overall}")
    
    print(f"\n" + "=" * 70)
    print("🎯 POTENZIALE CALCIOMERCATO")
    print("=" * 70)
    
    # Squadre per livello di budget
    levels = [
        ("🌟 World Class", lambda c: c.budget > 500000000),
        ("⭐ Elite", lambda c: 200000000 < c.budget <= 500000000),
        ("🔥 Top", lambda c: 100000000 < c.budget <= 200000000),
        ("⚽ Good", lambda c: 50000000 < c.budget <= 100000000),
        ("📈 Medium", lambda c: 10000000 < c.budget <= 50000000),
        ("🏃 Small", lambda c: c.budget <= 10000000)
    ]
    
    for level_name, condition in levels:
        level_clubs = [c for c in all_clubs if condition(c)]
        if level_clubs:
            avg_overall = sum(c.get_team_overall() for c in level_clubs) / len(level_clubs)
            print(f"   {level_name}: {len(level_clubs)} squadre (Overall medio: {avg_overall:.1f})")
    
    print(f"\n✅ SISTEMA COMPLETO FUNZIONANTE!")
    print(f"🌍 Database globale pronto per il calciomercato!")
    print(f"🎮 {len(all_clubs)} squadre e {total_players} giocatori disponibili!")
    
    return len(clubs_without_players) == 0

if __name__ == "__main__":
    success = test_complete_system()
    sys.exit(0 if success else 1)
