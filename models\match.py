"""
Modello per rappresentare una partita di calcio
"""

from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from enum import Enum
from datetime import datetime
import random

from .club import Club
from .player import Player

class MatchType(Enum):
    """Tipo di partita"""
    CAMPIONATO = "campionato"
    COPPA_ITALIA = "coppa_italia"
    AMICHEVOLE = "amichevole"

class MatchStatus(Enum):
    """Stato della partita"""
    PROGRAMMATA = "programmata"
    IN_CORSO = "in_corso"
    FINITA = "finita"
    RINVIATA = "rinviata"

@dataclass
class MatchEvent:
    """Evento durante la partita"""
    minuto: int
    tipo: str  # "gol", "cartellino_giallo", "cartellino_rosso", "sostituzione"
    giocatore: Player
    descrizione: str

@dataclass
class MatchStats:
    """Statistiche della partita"""
    possesso_palla: Tuple[int, int]  # (casa, trasferta)
    tiri_totali: Tuple[int, int]
    tiri_in_porta: Tuple[int, int]
    calci_angolo: Tuple[int, int]
    falli: Tuple[int, int]
    cartellini_gialli: Tuple[int, int]
    cartellini_rossi: Tuple[int, int]

class Match:
    """Classe che rappresenta una partita"""
    
    def __init__(self,
                 match_id: int,
                 squadra_casa: Club,
                 squadra_trasferta: Club,
                 data: datetime,
                 tipo: MatchType = MatchType.CAMPIONATO,
                 giornata: int = 1):
        
        self.id = match_id
        self.squadra_casa = squadra_casa
        self.squadra_trasferta = squadra_trasferta
        self.data = data
        self.tipo = tipo
        self.giornata = giornata
        
        # Risultato
        self.gol_casa = 0
        self.gol_trasferta = 0
        self.status = MatchStatus.PROGRAMMATA
        
        # Formazioni
        self.formazione_casa: List[Player] = []
        self.formazione_trasferta: List[Player] = []
        
        # Eventi e statistiche
        self.eventi: List[MatchEvent] = []
        self.stats: Optional[MatchStats] = None
        
        # Spettatori
        self.spettatori = 0
        self.incasso = 0
    
    def set_formations(self, 
                      formazione_casa: List[Player], 
                      formazione_trasferta: List[Player]):
        """Imposta le formazioni delle squadre"""
        self.formazione_casa = formazione_casa[:11]  # Massimo 11 giocatori
        self.formazione_trasferta = formazione_trasferta[:11]
    
    def simulate_match(self) -> Tuple[int, int]:
        """Simula la partita e restituisce il risultato"""
        if self.status != MatchStatus.PROGRAMMATA:
            return self.gol_casa, self.gol_trasferta
        
        self.status = MatchStatus.IN_CORSO
        
        # Calcola la forza delle squadre
        forza_casa = self._calculate_team_strength(self.squadra_casa, True)
        forza_trasferta = self._calculate_team_strength(self.squadra_trasferta, False)
        
        # Simula gli eventi della partita
        self._simulate_match_events(forza_casa, forza_trasferta)
        
        # Genera statistiche
        self._generate_match_stats(forza_casa, forza_trasferta)
        
        # Calcola spettatori e incasso
        self._calculate_attendance_and_revenue()
        
        # Aggiorna statistiche dei club
        self._update_club_stats()
        
        self.status = MatchStatus.FINITA
        return self.gol_casa, self.gol_trasferta
    
    def _calculate_team_strength(self, club: Club, is_home: bool) -> float:
        """Calcola la forza di una squadra"""
        # Forza base dalla rosa
        base_strength = club.get_team_overall()
        
        # Bonus casa
        home_bonus = 5 if is_home else 0
        
        # Bonus allenatore
        allenatore = club.get_staff_by_role("allenatore")
        coach_bonus = allenatore.stats.overall() / 10 if allenatore else 0
        
        # Morale della squadra
        if club.giocatori:
            team_morale = sum(p.morale for p in club.giocatori) / len(club.giocatori)
            morale_bonus = (team_morale - 75) / 5  # Bonus/malus basato sul morale
        else:
            morale_bonus = 0
        
        total_strength = base_strength + home_bonus + coach_bonus + morale_bonus
        return max(30, min(100, total_strength))
    
    def _simulate_match_events(self, forza_casa: float, forza_trasferta: float):
        """Simula gli eventi della partita"""
        # Probabilità di gol basata sulla differenza di forza
        diff_forza = forza_casa - forza_trasferta
        
        # Probabilità base di gol per minuto
        prob_gol_casa = (0.02 + diff_forza * 0.0005) / 90
        prob_gol_trasferta = (0.02 - diff_forza * 0.0005) / 90
        
        # Simula ogni minuto
        for minuto in range(1, 91):
            # Possibili gol
            if random.random() < prob_gol_casa:
                self._add_goal_event(minuto, True)
            elif random.random() < prob_gol_trasferta:
                self._add_goal_event(minuto, False)
            
            # Possibili cartellini (molto rari)
            if random.random() < 0.001:  # 0.1% per minuto
                self._add_card_event(minuto, random.choice([True, False]))
    
    def _add_goal_event(self, minuto: int, is_home_team: bool):
        """Aggiunge un evento gol"""
        if is_home_team:
            self.gol_casa += 1
            team_players = self.formazione_casa
            if not team_players:
                team_players = [p for p in self.squadra_casa.giocatori if p.is_available()]
        else:
            self.gol_trasferta += 1
            team_players = self.formazione_trasferta
            if not team_players:
                team_players = [p for p in self.squadra_trasferta.giocatori if p.is_available()]
        
        if team_players:
            # Probabilità maggiore per attaccanti e centrocampisti
            scorer_weights = []
            for player in team_players:
                if player.posizione.value == "A":
                    scorer_weights.append(3)
                elif player.posizione.value == "C":
                    scorer_weights.append(2)
                elif player.posizione.value == "D":
                    scorer_weights.append(1)
                else:  # Portiere
                    scorer_weights.append(0.1)
            
            scorer = random.choices(team_players, weights=scorer_weights)[0]
            scorer.gol += 1
            
            event = MatchEvent(
                minuto=minuto,
                tipo="gol",
                giocatore=scorer,
                descrizione=f"Gol di {scorer.nome_completo()}"
            )
            self.eventi.append(event)
    
    def _add_card_event(self, minuto: int, is_home_team: bool):
        """Aggiunge un evento cartellino"""
        if is_home_team:
            team_players = self.formazione_casa or [p for p in self.squadra_casa.giocatori if p.is_available()]
        else:
            team_players = self.formazione_trasferta or [p for p in self.squadra_trasferta.giocatori if p.is_available()]
        
        if team_players:
            player = random.choice(team_players)
            card_type = "cartellino_rosso" if random.random() < 0.2 else "cartellino_giallo"
            
            if card_type == "cartellino_giallo":
                player.cartellini_gialli += 1
            else:
                player.cartellini_rossi += 1
                player.giornate_squalifica += 1
            
            event = MatchEvent(
                minuto=minuto,
                tipo=card_type,
                giocatore=player,
                descrizione=f"{card_type.replace('_', ' ').title()} per {player.nome_completo()}"
            )
            self.eventi.append(event)
    
    def _generate_match_stats(self, forza_casa: float, forza_trasferta: float):
        """Genera le statistiche della partita"""
        # Possesso palla basato sulla forza
        total_strength = forza_casa + forza_trasferta
        possesso_casa = int((forza_casa / total_strength) * 100)
        possesso_trasferta = 100 - possesso_casa
        
        # Tiri basati su gol e possesso
        tiri_casa = max(self.gol_casa * 2, random.randint(5, 20))
        tiri_trasferta = max(self.gol_trasferta * 2, random.randint(5, 20))
        
        tiri_porta_casa = max(self.gol_casa, random.randint(2, tiri_casa // 2))
        tiri_porta_trasferta = max(self.gol_trasferta, random.randint(2, tiri_trasferta // 2))
        
        self.stats = MatchStats(
            possesso_palla=(possesso_casa, possesso_trasferta),
            tiri_totali=(tiri_casa, tiri_trasferta),
            tiri_in_porta=(tiri_porta_casa, tiri_porta_trasferta),
            calci_angolo=(random.randint(2, 12), random.randint(2, 12)),
            falli=(random.randint(8, 20), random.randint(8, 20)),
            cartellini_gialli=(
                len([e for e in self.eventi if e.tipo == "cartellino_giallo" and e.giocatore in self.formazione_casa]),
                len([e for e in self.eventi if e.tipo == "cartellino_giallo" and e.giocatore in self.formazione_trasferta])
            ),
            cartellini_rossi=(
                len([e for e in self.eventi if e.tipo == "cartellino_rosso" and e.giocatore in self.formazione_casa]),
                len([e for e in self.eventi if e.tipo == "cartellino_rosso" and e.giocatore in self.formazione_trasferta])
            )
        )
    
    def _calculate_attendance_and_revenue(self):
        """Calcola spettatori e incasso"""
        # Spettatori basati sulla capacità dello stadio e attrattiva della partita
        max_attendance = self.squadra_casa.stadio_capacita
        
        # Fattori che influenzano la presenza
        league_factor = {"Serie A": 0.8, "Serie B": 0.6, "Serie C": 0.4}.get(
            self.squadra_casa.campionato.value, 0.3
        )
        
        fan_satisfaction_factor = self.squadra_casa.tifosi_soddisfazione / 100
        
        expected_attendance = int(max_attendance * league_factor * fan_satisfaction_factor)
        self.spettatori = random.randint(
            int(expected_attendance * 0.7), 
            min(max_attendance, int(expected_attendance * 1.2))
        )
        
        # Prezzo medio del biglietto
        ticket_price = {"Serie A": 25, "Serie B": 15, "Serie C": 10}.get(
            self.squadra_casa.campionato.value, 8
        )
        
        self.incasso = self.spettatori * ticket_price
    
    def _update_club_stats(self):
        """Aggiorna le statistiche dei club"""
        # Aggiorna statistiche squadra casa
        self.squadra_casa.partite_giocate += 1
        self.squadra_casa.gol_fatti += self.gol_casa
        self.squadra_casa.gol_subiti += self.gol_trasferta
        
        # Aggiorna statistiche squadra trasferta
        self.squadra_trasferta.partite_giocate += 1
        self.squadra_trasferta.gol_fatti += self.gol_trasferta
        self.squadra_trasferta.gol_subiti += self.gol_casa
        
        # Determina il risultato
        if self.gol_casa > self.gol_trasferta:
            # Vittoria casa
            self.squadra_casa.vittorie += 1
            self.squadra_casa.punti += 3
            self.squadra_trasferta.sconfitte += 1
        elif self.gol_casa < self.gol_trasferta:
            # Vittoria trasferta
            self.squadra_trasferta.vittorie += 1
            self.squadra_trasferta.punti += 3
            self.squadra_casa.sconfitte += 1
        else:
            # Pareggio
            self.squadra_casa.pareggi += 1
            self.squadra_casa.punti += 1
            self.squadra_trasferta.pareggi += 1
            self.squadra_trasferta.punti += 1
        
        # Aggiorna presenze giocatori
        for player in self.formazione_casa + self.formazione_trasferta:
            player.presenze += 1
    
    def get_result_string(self) -> str:
        """Restituisce il risultato come stringa"""
        if self.status != MatchStatus.FINITA:
            return "Non giocata"
        return f"{self.squadra_casa.nome} {self.gol_casa}-{self.gol_trasferta} {self.squadra_trasferta.nome}"
    
    def __str__(self):
        return f"{self.squadra_casa.nome} vs {self.squadra_trasferta.nome} - {self.data.strftime('%d/%m/%Y')}"
