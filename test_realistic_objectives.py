#!/usr/bin/env python3
"""
Test del nuovo sistema di obiettivi realistici
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.club import Club, League
from core.player_generator import player_generator
from utils.json_loader import data_loader

def test_realistic_objectives():
    """Test del sistema di obiettivi realistici"""
    print("🎯 Test Sistema Obiettivi Realistici")
    print("=" * 60)
    
    # Crea club di test con diverse caratteristiche
    test_clubs_data = [
        # Nome, Campionato, Budget_mln, Stadio_capacita, Tradizione_attesa
        ("Juventus", League.SERIE_A, 500.0, 41507, "Alta"),
        ("Inter", League.SERIE_A, 600.0, 75923, "Alta"),
        ("Milan", League.SERIE_A, 550.0, 75923, "Alta"),
        ("Atalanta", League.SERIE_A, 80.0, 21747, "Media"),
        ("Empoli", League.SERIE_A, 25.0, 16284, "Bassa"),
        ("Lecce", League.SERIE_A, 20.0, 31533, "Bassa"),
        
        ("Parma", League.SERIE_B, 45.0, 27906, "Media"),
        ("Palermo", League.SERIE_B, 35.0, 36365, "Media"),
        ("Brescia", League.SERIE_B, 15.0, 16743, "Media"),
        ("Cittadella", League.SERIE_B, 8.0, 7623, "Bassa"),
        ("Ternana", League.SERIE_B, 6.0, 17460, "Bassa"),
        
        ("Juventus U23", League.SERIE_C, 10.0, 41507, "Speciale"),
        ("Padova", League.SERIE_C, 5.0, 18060, "Media"),
        ("Pro Vercelli", League.SERIE_C, 2.0, 5000, "Bassa"),
        ("Triestina", League.SERIE_C, 1.5, 32454, "Media"),
        
        ("Real Madrid", League.EUROPA, 900.0, 81044, "Altissima"),
        ("Manchester City", League.EUROPA, 1000.0, 55017, "Media"),
        ("Brighton", League.EUROPA, 120.0, 30750, "Bassa"),
        ("Girona", League.EUROPA, 45.0, 13500, "Bassa"),
        ("Celtic FC", League.EUROPA, 80.0, 60411, "Alta")
    ]
    
    test_clubs = []
    
    print("🏗️ Creazione club di test...")
    
    for nome, campionato, budget_mln, stadio_capacita, tradizione in test_clubs_data:
        budget = int(budget_mln * 1000000)
        
        club = Club(
            club_id=len(test_clubs) + 1,
            nome=nome,
            citta=nome,
            campionato=campionato,
            budget=budget,
            stadio_nome=f"Stadio {nome}",
            stadio_capacita=stadio_capacita,
            budget_mln=budget_mln
        )
        
        test_clubs.append(club)
    
    print(f"✅ Creati {len(test_clubs)} club di test")
    
    # Analisi obiettivi per campionato
    print("\n" + "=" * 60)
    print("📊 ANALISI OBIETTIVI PER CAMPIONATO")
    print("=" * 60)
    
    # Raggruppa per campionato
    leagues = {}
    for club in test_clubs:
        league_name = club.campionato.value
        if league_name not in leagues:
            leagues[league_name] = []
        leagues[league_name].append(club)
    
    for league_name, clubs in leagues.items():
        print(f"\n🏆 {league_name.upper()}:")
        print("-" * 50)
        
        # Ordina per budget
        clubs.sort(key=lambda c: c.budget_mln, reverse=True)
        
        objective_distribution = {}
        
        for club in clubs:
            # Calcola il punteggio per debug
            score = club._calculate_objective_score()
            budget_score = min(club.budget_mln / 100, 10.0) * 0.4
            stadium_score = club._get_stadium_score() * 0.25
            tradition_score = club._get_tradition_score() * 0.2
            
            objective = club.obiettivo_principale
            if objective not in objective_distribution:
                objective_distribution[objective] = []
            objective_distribution[objective].append(club.nome)
            
            print(f"📋 {club.nome}:")
            print(f"   💰 Budget: €{club.budget_mln:.1f}M")
            print(f"   🏟️  Stadio: {club.stadio_capacita:,} posti")
            print(f"   📈 Punteggio totale: {score:.2f}")
            print(f"      • Budget: {budget_score:.2f}")
            print(f"      • Stadio: {stadium_score:.2f}")
            print(f"      • Tradizione: {tradition_score:.2f}")
            print(f"   🎯 Obiettivo: {objective}")
            print(f"   📝 Obiettivi secondari: {', '.join(club.obiettivi_secondari)}")
            print()
        
        # Mostra distribuzione obiettivi
        print("📊 Distribuzione obiettivi:")
        for objective, clubs_list in objective_distribution.items():
            print(f"   • {objective}: {len(clubs_list)} club ({', '.join(clubs_list)})")
        print()
    
    # Test variabilità obiettivi
    print("=" * 60)
    print("🔄 TEST VARIABILITÀ OBIETTIVI")
    print("=" * 60)
    
    # Testa la stessa squadra più volte per vedere la variabilità
    test_club = Club(
        club_id=999,
        nome="Test FC",
        citta="Test City",
        campionato=League.SERIE_A,
        budget=int(50 * 1000000),
        stadio_nome="Test Stadium",
        stadio_capacita=25000,
        budget_mln=50.0
    )
    
    objectives_count = {}
    num_tests = 20
    
    print(f"🔬 Testando {test_club.nome} (€{test_club.budget_mln:.1f}M, {test_club.stadio_capacita:,} posti) {num_tests} volte:")
    
    for i in range(num_tests):
        # Ricrea il club per ricalcolare l'obiettivo
        test_club_copy = Club(
            club_id=999 + i,
            nome="Test FC",
            citta="Test City",
            campionato=League.SERIE_A,
            budget=int(50 * 1000000),
            stadio_nome="Test Stadium",
            stadio_capacita=25000,
            budget_mln=50.0
        )
        
        objective = test_club_copy.obiettivo_principale
        objectives_count[objective] = objectives_count.get(objective, 0) + 1
    
    print("📊 Distribuzione obiettivi:")
    for objective, count in sorted(objectives_count.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / num_tests) * 100
        print(f"   • {objective}: {count}/{num_tests} ({percentage:.1f}%)")
    
    # Test influenza stadio
    print("\n" + "=" * 60)
    print("🏟️ TEST INFLUENZA STADIO")
    print("=" * 60)
    
    stadium_tests = [
        ("Piccolo Stadio", 8000),
        ("Stadio Medio", 25000),
        ("Grande Stadio", 50000),
        ("Stadio Gigante", 80000)
    ]
    
    for stadium_name, capacity in stadium_tests:
        objectives_stadium = {}
        
        for i in range(10):
            club = Club(
                club_id=1000 + i,
                nome="Stadium Test FC",
                citta="Test City",
                campionato=League.SERIE_A,
                budget=int(50 * 1000000),  # Budget fisso
                stadio_nome=stadium_name,
                stadio_capacita=capacity,
                budget_mln=50.0
            )
            
            objective = club.obiettivo_principale
            objectives_stadium[objective] = objectives_stadium.get(objective, 0) + 1
        
        print(f"🏟️ {stadium_name} ({capacity:,} posti):")
        for objective, count in objectives_stadium.items():
            print(f"   • {objective}: {count}/10 volte")
        print()
    
    # Test influenza tradizione
    print("=" * 60)
    print("🏛️ TEST INFLUENZA TRADIZIONE")
    print("=" * 60)
    
    tradition_tests = [
        ("Club Storico", "Juventus"),
        ("Club Normale", "Club Generico"),
        ("Club Europeo Top", "Real Madrid"),
        ("Club Europeo Normale", "Brighton")
    ]
    
    for test_name, club_name in tradition_tests:
        objectives_tradition = {}
        
        for i in range(10):
            club = Club(
                club_id=2000 + i,
                nome=club_name,
                citta="Test City",
                campionato=League.SERIE_A if "Real Madrid" not in club_name and "Brighton" not in club_name else League.EUROPA,
                budget=int(50 * 1000000),  # Budget fisso
                stadio_nome="Test Stadium",
                stadio_capacita=25000,  # Stadio fisso
                budget_mln=50.0
            )
            
            objective = club.obiettivo_principale
            objectives_tradition[objective] = objectives_tradition.get(objective, 0) + 1
        
        print(f"🏛️ {test_name} ({club_name}):")
        tradition_score = Club(1, club_name, "Test", League.SERIE_A, 1000000, "Test", 1000, 1.0)._get_tradition_score()
        print(f"   Punteggio tradizione: {tradition_score}")
        for objective, count in objectives_tradition.items():
            print(f"   • {objective}: {count}/10 volte")
        print()
    
    print("✅ Test obiettivi realistici completato!")
    print("\n🎯 MIGLIORAMENTI IMPLEMENTATI:")
    print("   • Obiettivi basati su budget + stadio + tradizione + casualità")
    print("   • Maggiore variabilità e realismo")
    print("   • Club storici hanno ambizioni più alte")
    print("   • Stadi grandi influenzano le aspettative")
    print("   • Obiettivi secondari più specifici e contestuali")
    
    return True

if __name__ == "__main__":
    success = test_realistic_objectives()
    sys.exit(0 if success else 1)
