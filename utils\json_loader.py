"""
Utilità per caricare dati da file JSON e CSV
"""

import json
import csv
import os
from typing import List, Dict, Any, Optional
import pandas as pd

class DataLoader:
    """Classe per caricare dati da vari formati"""
    
    def __init__(self, data_dir: str = "data/"):
        self.data_dir = data_dir
    
    def load_json(self, filename: str) -> Optional[Dict[str, Any]]:
        """Carica dati da un file JSON"""
        filepath = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(filepath):
            print(f"File non trovato: {filepath}")
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"Errore nel caricamento di {filepath}: {e}")
            return None
    
    def load_csv(self, filename: str) -> Optional[List[Dict[str, Any]]]:
        """Carica dati da un file CSV"""
        filepath = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(filepath):
            print(f"File non trovato: {filepath}")
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                return list(reader)
        except (csv.Error, IOError) as e:
            print(f"Errore nel caricamento di {filepath}: {e}")
            return None
    
    def load_csv_pandas(self, filename: str) -> Optional[pd.DataFrame]:
        """Carica dati da un file CSV usando pandas"""
        filepath = os.path.join(self.data_dir, filename)
        
        if not os.path.exists(filepath):
            print(f"File non trovato: {filepath}")
            return None
        
        try:
            return pd.read_csv(filepath, encoding='utf-8')
        except Exception as e:
            print(f"Errore nel caricamento di {filepath}: {e}")
            return None
    
    def save_json(self, data: Dict[str, Any], filename: str) -> bool:
        """Salva dati in un file JSON"""
        filepath = os.path.join(self.data_dir, filename)
        
        # Crea la directory se non esiste
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"Errore nel salvataggio di {filepath}: {e}")
            return False
    
    def save_csv(self, data: List[Dict[str, Any]], filename: str) -> bool:
        """Salva dati in un file CSV"""
        if not data:
            return False
        
        filepath = os.path.join(self.data_dir, filename)
        
        # Crea la directory se non esiste
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
            return True
        except IOError as e:
            print(f"Errore nel salvataggio di {filepath}: {e}")
            return False
    
    def load_teams_data(self, league: str) -> Optional[List[Dict[str, Any]]]:
        """Carica i dati delle squadre per un campionato specifico"""
        filename_map = {
            "serie_a": "serie_a.csv",
            "serie_b": "serie_b.csv",
            "serie_c_a": "serie_c_girone_A.csv",
            "serie_c_b": "serie_c_girone_B.csv",
            "serie_c_c": "serie_c_girone_C.csv"
        }
        
        filename = filename_map.get(league.lower())
        if not filename:
            print(f"Campionato non riconosciuto: {league}")
            return None
        
        return self.load_csv(filename)
    
    def load_player_names(self) -> Optional[Dict[str, List[str]]]:
        """Carica i nomi dei giocatori"""
        return self.load_json("player_names.json")
    
    def get_available_leagues(self) -> List[str]:
        """Restituisce la lista dei campionati disponibili"""
        leagues = []
        
        league_files = [
            ("Serie A", "serie_a.csv"),
            ("Serie B", "serie_b.csv"),
            ("Serie C Girone A", "serie_c_girone_A.csv"),
            ("Serie C Girone B", "serie_c_girone_B.csv"),
            ("Serie C Girone C", "serie_c_girone_C.csv")
        ]
        
        for league_name, filename in league_files:
            filepath = os.path.join(self.data_dir, filename)
            if os.path.exists(filepath):
                leagues.append(league_name)
        
        return leagues
    
    def validate_team_data(self, team_data: Dict[str, Any]) -> bool:
        """Valida i dati di una squadra"""
        # Accetta sia 'nome' che 'squadra' come nome del club
        has_name = ('nome' in team_data and team_data['nome']) or ('squadra' in team_data and team_data['squadra'])

        if not has_name:
            return False

        return True
    
    def clean_team_data(self, teams_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Pulisce e normalizza i dati delle squadre"""
        cleaned_data = []
        
        for team in teams_data:
            if self.validate_team_data(team):
                # Normalizza i dati
                nome_club = team.get('nome', team.get('squadra', 'Club Sconosciuto')).strip()
                cleaned_team = {
                    'nome': nome_club,
                    'citta': team.get('citta', nome_club).strip(),
                    'stadio': team.get('stadio', '').strip(),
                    'capacita': self._parse_int(team.get('capacita', team.get('capienza_stadio', '10000'))),
                    'anno_fondazione': self._parse_int(team.get('anno_fondazione', '1900'))
                }
                cleaned_data.append(cleaned_team)
        
        return cleaned_data
    
    def _parse_int(self, value: str, default: int = 0) -> int:
        """Converte una stringa in intero con valore di default"""
        try:
            return int(str(value).replace(',', '').replace('.', ''))
        except (ValueError, TypeError):
            return default

# Istanza globale del data loader
data_loader = DataLoader()
