"""
Widget dashboard principale del gioco
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QProgressBar, QListWidget,
                            QListWidgetItem, QScrollArea, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPalette

from models.club import Club
from models.player import Position

class InfoCard(QFrame):
    """Card per mostrare informazioni"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", color: str = "#2196F3"):
        super().__init__()
        
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedHeight(100)
        self.setStyleSheet(f"""
            QFrame {{
                border-left: 4px solid {color};
                background-color: #f8f9fa;
                border-radius: 5px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        
        # Titolo
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setStyleSheet("color: #666;")
        layout.addWidget(title_label)
        
        # Valore principale
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # Sottotitolo
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setFont(QFont("Arial", 8))
            subtitle_label.setStyleSheet("color: #888;")
            layout.addWidget(subtitle_label)

class DashboardWidget(QWidget):
    """Widget principale della dashboard"""
    
    def __init__(self, club: Club):
        super().__init__()
        
        self.club = club
        self.init_ui()
        self.update_data()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        layout = QVBoxLayout(self)
        
        # Scroll area per contenuto
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        layout.addWidget(scroll)
        
        # Widget contenuto
        content_widget = QWidget()
        scroll.setWidget(content_widget)
        content_layout = QVBoxLayout(content_widget)
        
        # Sezione informazioni principali
        self.create_main_info_section(content_layout)
        
        # Sezione rosa
        self.create_squad_section(content_layout)
        
        # Sezione prossime partite
        self.create_fixtures_section(content_layout)
        
        # Sezione notizie/eventi
        self.create_news_section(content_layout)
    
    def create_main_info_section(self, parent_layout):
        """Crea la sezione con le informazioni principali"""
        section_label = QLabel("Panoramica")
        section_label.setFont(QFont("Arial", 14, QFont.Bold))
        parent_layout.addWidget(section_label)
        
        # Grid con le card informative
        cards_layout = QGridLayout()
        parent_layout.addLayout(cards_layout)
        
        # Card Budget Totale
        self.budget_card = InfoCard(
            "Budget Totale",
            f"€{self.club.budget_mln:.1f}M",
            f"€{self.club.budget:,} totali",
            "#4CAF50"
        )
        cards_layout.addWidget(self.budget_card, 0, 0)
        
        # Card Budget Trasferimenti
        self.transfer_budget_card = InfoCard(
            "Budget Trasferimenti",
            f"€{self.club.budget_trasferimenti / 1000000:.1f}M",
            f"€{self.club.budget_trasferimenti:,}",
            "#2196F3"
        )
        cards_layout.addWidget(self.transfer_budget_card, 0, 1)
        
        # Card Budget Stipendi
        stipendi_usati = (self.club.uscite_mensili / self.club.budget_stipendi * 100) if self.club.budget_stipendi > 0 else 0
        stipendi_color = "#4CAF50" if stipendi_usati < 80 else "#FF9800" if stipendi_usati < 95 else "#F44336"
        self.salary_budget_card = InfoCard(
            "Budget Stipendi",
            f"€{self.club.budget_stipendi / 1000:.0f}K/mese",
            f"{stipendi_usati:.1f}% utilizzato",
            stipendi_color
        )
        cards_layout.addWidget(self.salary_budget_card, 0, 2)
        
        # Card Livello Club
        club_tier = self.club.get_club_tier()
        tier_colors = {
            "World Class": "#FFD700", "Elite": "#C0C0C0", "Top": "#CD7F32",
            "Good": "#4CAF50", "Average": "#FF9800", "Small": "#9E9E9E"
        }
        self.tier_card = InfoCard(
            "Livello Club",
            club_tier,
            f"Obiettivo: {self.club.obiettivo_principale}",
            tier_colors.get(club_tier, "#9E9E9E")
        )
        cards_layout.addWidget(self.tier_card, 0, 3)
        
        # Card Overall Squadra
        team_overall = self.club.get_team_overall()
        self.overall_card = InfoCard(
            "Overall Squadra",
            str(team_overall),
            f"Media di {len(self.club.giocatori)} giocatori",
            "#9C27B0"
        )
        cards_layout.addWidget(self.overall_card, 1, 0)
        
        # Card Soddisfazione Tifosi
        satisfaction_color = self._get_satisfaction_color(self.club.tifosi_soddisfazione)
        self.fans_card = InfoCard(
            "Soddisfazione Tifosi",
            f"{self.club.tifosi_soddisfazione}%",
            "Supporto dei tifosi",
            satisfaction_color
        )
        cards_layout.addWidget(self.fans_card, 1, 1)
        
        # Card Gol Fatti/Subiti
        goal_diff = self.club.gol_fatti - self.club.gol_subiti
        goal_diff_text = f"+{goal_diff}" if goal_diff > 0 else str(goal_diff)
        goal_color = "#4CAF50" if goal_diff > 0 else "#F44336" if goal_diff < 0 else "#FF9800"
        self.goals_card = InfoCard(
            "Differenza Reti",
            goal_diff_text,
            f"{self.club.gol_fatti} fatti, {self.club.gol_subiti} subiti",
            goal_color
        )
        cards_layout.addWidget(self.goals_card, 1, 2)
        
        # Card Bilancio Mensile
        monthly_balance = self.club.get_monthly_balance()
        balance_color = "#4CAF50" if monthly_balance >= 0 else "#F44336"
        self.balance_card = InfoCard(
            "Bilancio Mensile",
            f"€{monthly_balance:,}",
            "Entrate - Uscite",
            balance_color
        )
        cards_layout.addWidget(self.balance_card, 1, 3)
    
    def create_squad_section(self, parent_layout):
        """Crea la sezione della rosa"""
        section_label = QLabel("Rosa")
        section_label.setFont(QFont("Arial", 14, QFont.Bold))
        parent_layout.addWidget(section_label)
        
        squad_frame = QFrame()
        squad_frame.setFrameStyle(QFrame.StyledPanel)
        squad_frame.setMaximumHeight(200)
        parent_layout.addWidget(squad_frame)
        
        squad_layout = QHBoxLayout(squad_frame)
        
        # Statistiche per posizione
        positions_layout = QGridLayout()
        squad_layout.addLayout(positions_layout)
        
        positions = [
            (Position.PORTIERE, "Portieri", "#FF5722"),
            (Position.DIFENSORE, "Difensori", "#2196F3"),
            (Position.CENTROCAMPISTA, "Centrocampisti", "#4CAF50"),
            (Position.ATTACCANTE, "Attaccanti", "#FF9800")
        ]
        
        for i, (position, name, color) in enumerate(positions):
            players = self.club.get_players_by_position(position)
            count = len(players)
            avg_overall = int(sum(p.stats.overall() for p in players) / count) if count > 0 else 0
            
            pos_card = InfoCard(
                name,
                str(count),
                f"Overall medio: {avg_overall}" if count > 0 else "Nessun giocatore",
                color
            )
            positions_layout.addWidget(pos_card, i // 2, i % 2)
        
        # Lista giocatori migliori
        squad_layout.addWidget(self.create_top_players_list())
    
    def create_top_players_list(self) -> QWidget:
        """Crea la lista dei migliori giocatori"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        frame.setMinimumWidth(300)
        
        layout = QVBoxLayout(frame)
        
        title_label = QLabel("Migliori Giocatori")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        self.top_players_list = QListWidget()
        self.top_players_list.setMaximumHeight(150)
        layout.addWidget(self.top_players_list)
        
        return frame
    
    def create_fixtures_section(self, parent_layout):
        """Crea la sezione delle prossime partite"""
        section_label = QLabel("Prossime Partite")
        section_label.setFont(QFont("Arial", 14, QFont.Bold))
        parent_layout.addWidget(section_label)
        
        fixtures_frame = QFrame()
        fixtures_frame.setFrameStyle(QFrame.StyledPanel)
        fixtures_frame.setMaximumHeight(150)
        parent_layout.addWidget(fixtures_frame)
        
        fixtures_layout = QVBoxLayout(fixtures_frame)
        
        # Placeholder per le partite
        no_fixtures_label = QLabel("Nessuna partita programmata")
        no_fixtures_label.setAlignment(Qt.AlignCenter)
        no_fixtures_label.setStyleSheet("color: #666; font-style: italic;")
        fixtures_layout.addWidget(no_fixtures_label)
    
    def create_news_section(self, parent_layout):
        """Crea la sezione notizie/eventi"""
        section_label = QLabel("Notizie e Eventi")
        section_label.setFont(QFont("Arial", 14, QFont.Bold))
        parent_layout.addWidget(section_label)
        
        news_frame = QFrame()
        news_frame.setFrameStyle(QFrame.StyledPanel)
        news_frame.setMaximumHeight(150)
        parent_layout.addWidget(news_frame)
        
        news_layout = QVBoxLayout(news_frame)
        
        self.news_list = QListWidget()
        news_layout.addWidget(self.news_list)
        
        # Aggiungi alcune notizie di esempio
        self.add_news_item("Benvenuto come nuovo presidente!", "info")
        self.add_news_item("La stagione inizierà presto", "info")
        self.add_news_item("Considera di assumere uno staff tecnico", "warning")
    
    def add_news_item(self, text: str, item_type: str = "info"):
        """Aggiunge un elemento alle notizie"""
        item = QListWidgetItem(text)
        
        if item_type == "warning":
            item.setBackground(QColor("#FFF3CD"))
        elif item_type == "error":
            item.setBackground(QColor("#F8D7DA"))
        elif item_type == "success":
            item.setBackground(QColor("#D4EDDA"))
        else:  # info
            item.setBackground(QColor("#D1ECF1"))
        
        self.news_list.addItem(item)
    
    def update_data(self):
        """Aggiorna tutti i dati della dashboard"""
        self.update_main_info()
        self.update_squad_info()
        self.update_top_players()
    
    def update_main_info(self):
        """Aggiorna le informazioni principali"""
        # Aggiorna budget totale
        budget_labels = self.budget_card.findChildren(QLabel)
        budget_labels[1].setText(f"€{self.club.budget_mln:.1f}M")

        # Aggiorna budget trasferimenti
        transfer_labels = self.transfer_budget_card.findChildren(QLabel)
        transfer_labels[1].setText(f"€{self.club.budget_trasferimenti / 1000000:.1f}M")

        # Aggiorna budget stipendi
        stipendi_usati = (self.club.uscite_mensili / self.club.budget_stipendi * 100) if self.club.budget_stipendi > 0 else 0
        stipendi_color = "#4CAF50" if stipendi_usati < 80 else "#FF9800" if stipendi_usati < 95 else "#F44336"
        salary_labels = self.salary_budget_card.findChildren(QLabel)
        salary_labels[1].setText(f"€{self.club.budget_stipendi / 1000:.0f}K/mese")
        salary_labels[1].setStyleSheet(f"color: {stipendi_color};")
        salary_labels[2].setText(f"{stipendi_usati:.1f}% utilizzato")

        # Aggiorna livello club
        club_tier = self.club.get_club_tier()
        tier_labels = self.tier_card.findChildren(QLabel)
        tier_labels[1].setText(club_tier)
        tier_labels[2].setText(f"Obiettivo: {self.club.obiettivo_principale}")

        # Aggiorna overall squadra
        team_overall = self.club.get_team_overall()
        overall_labels = self.overall_card.findChildren(QLabel)
        overall_labels[1].setText(str(team_overall))

        # Aggiorna soddisfazione tifosi
        satisfaction_color = self._get_satisfaction_color(self.club.tifosi_soddisfazione)
        fans_labels = self.fans_card.findChildren(QLabel)
        fans_labels[1].setText(f"{self.club.tifosi_soddisfazione}%")
        fans_labels[1].setStyleSheet(f"color: {satisfaction_color};")

        # Aggiorna differenza reti
        goal_diff = self.club.gol_fatti - self.club.gol_subiti
        goal_diff_text = f"+{goal_diff}" if goal_diff > 0 else str(goal_diff)
        goal_color = "#4CAF50" if goal_diff > 0 else "#F44336" if goal_diff < 0 else "#FF9800"
        goals_labels = self.goals_card.findChildren(QLabel)
        goals_labels[1].setText(goal_diff_text)
        goals_labels[1].setStyleSheet(f"color: {goal_color};")

        # Aggiorna bilancio mensile
        monthly_balance = self.club.get_monthly_balance()
        balance_color = "#4CAF50" if monthly_balance >= 0 else "#F44336"
        balance_labels = self.balance_card.findChildren(QLabel)
        balance_labels[1].setText(f"€{monthly_balance:,}")
        balance_labels[1].setStyleSheet(f"color: {balance_color};")
    
    def update_squad_info(self):
        """Aggiorna le informazioni della rosa"""
        # Questo metodo può essere espanso per aggiornare le statistiche per posizione
        pass
    
    def update_top_players(self):
        """Aggiorna la lista dei migliori giocatori"""
        self.top_players_list.clear()
        
        # Ordina giocatori per overall
        sorted_players = sorted(self.club.giocatori, 
                              key=lambda p: p.stats.overall(), 
                              reverse=True)
        
        # Mostra i primi 5
        for player in sorted_players[:5]:
            item_text = f"{player.nome_completo()} ({player.posizione.value}) - {player.stats.overall()}"
            item = QListWidgetItem(item_text)
            self.top_players_list.addItem(item)
    
    def _get_satisfaction_color(self, satisfaction: int) -> str:
        """Restituisce il colore basato sulla soddisfazione"""
        if satisfaction >= 80:
            return "#4CAF50"  # Verde
        elif satisfaction >= 60:
            return "#FF9800"  # Arancione
        elif satisfaction >= 40:
            return "#FF5722"  # Rosso-arancione
        else:
            return "#F44336"  # Rosso
