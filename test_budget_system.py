#!/usr/bin/env python3
"""
Test del nuovo sistema basato su budget_mln
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.club import Club, League
from models.player import Position
from core.player_generator import player_generator
from utils.json_loader import data_loader

def test_budget_system():
    """Test del sistema budget_mln"""
    print("💰 Test Sistema Budget Basato su budget_mln")
    print("=" * 60)
    
    # Test con squadre di diversi livelli
    test_clubs = []
    
    # Crea club di test con diversi budget
    test_data = [
        ("Juventus", League.SERIE_A, 300.0),
        ("Inter", League.SERIE_A, 250.0),
        ("Napoli", League.SERIE_A, 150.0),
        ("Atalanta", League.SERIE_A, 80.0),
        ("Empoli", League.SERIE_A, 25.0),
        ("Parma", League.SERIE_B, 30.0),
        ("Brescia", League.SERIE_B, 15.0),
        ("Modena", League.SERIE_B, 8.0),
        ("Juventus U23", League.SERIE_C, 10.0),
        ("Padova", League.SERIE_C, 5.0),
        ("Pro Vercelli", League.SERIE_C, 2.0),
        ("Real Madrid", League.EUROPA, 900.0),
        ("Manchester City", League.EUROPA, 1000.0),
        ("Bayern München", League.EUROPA, 850.0),
        ("Brighton", League.EUROPA, 120.0),
        ("Girona", League.EUROPA, 45.0)
    ]
    
    print("🏗️ Creazione club di test...")
    
    for i, (nome, campionato, budget_mln) in enumerate(test_data):
        budget = int(budget_mln * 1000000)
        
        club = Club(
            club_id=i + 1,
            nome=nome,
            citta=nome,
            campionato=campionato,
            budget=budget,
            stadio_nome=f"Stadio {nome}",
            stadio_capacita=50000,
            budget_mln=budget_mln
        )
        
        # Genera rosa
        squad = player_generator.generate_squad(club)
        for player in squad:
            club.add_player(player)
        
        test_clubs.append(club)
    
    print(f"✅ Creati {len(test_clubs)} club di test")
    
    # Analisi dettagliata
    print("\n" + "=" * 60)
    print("📊 ANALISI BUDGET E OBIETTIVI")
    print("=" * 60)
    
    # Raggruppa per campionato
    leagues = {}
    for club in test_clubs:
        league_name = club.campionato.value
        if league_name not in leagues:
            leagues[league_name] = []
        leagues[league_name].append(club)
    
    for league_name, clubs in leagues.items():
        print(f"\n🏆 {league_name.upper()}:")
        print("-" * 50)
        
        # Ordina per budget
        clubs.sort(key=lambda c: c.budget_mln, reverse=True)
        
        for club in clubs:
            budget_info = club.get_budget_breakdown()
            
            print(f"📋 {club.nome}:")
            print(f"   💰 Budget: €{club.budget_mln:.1f}M")
            print(f"   🎯 Livello: {club.get_club_tier()}")
            print(f"   🏆 Obiettivo: {club.obiettivo_principale}")
            print(f"   💸 Trasferimenti: €{club.budget_trasferimenti / 1000000:.1f}M")
            print(f"   💵 Stipendi: €{club.budget_stipendi / 1000:.0f}K/mese")
            print(f"   📈 Overall: {club.get_team_overall()}")
            print(f"   💰 Stipendi usati: {budget_info['percentuale_stipendi_usata']:.1f}%")
            print()
    
    # Analisi correlazione budget-overall
    print("=" * 60)
    print("📈 CORRELAZIONE BUDGET-OVERALL")
    print("=" * 60)
    
    # Raggruppa per livello di budget
    budget_tiers = {
        "Mega (€500M+)": [c for c in test_clubs if c.budget_mln >= 500],
        "Elite (€200-500M)": [c for c in test_clubs if 200 <= c.budget_mln < 500],
        "Top (€100-200M)": [c for c in test_clubs if 100 <= c.budget_mln < 200],
        "Good (€50-100M)": [c for c in test_clubs if 50 <= c.budget_mln < 100],
        "Medium (€20-50M)": [c for c in test_clubs if 20 <= c.budget_mln < 50],
        "Small (€5-20M)": [c for c in test_clubs if 5 <= c.budget_mln < 20],
        "Tiny (<€5M)": [c for c in test_clubs if c.budget_mln < 5]
    }
    
    for tier_name, clubs in budget_tiers.items():
        if clubs:
            avg_overall = sum(c.get_team_overall() for c in clubs) / len(clubs)
            avg_budget = sum(c.budget_mln for c in clubs) / len(clubs)
            print(f"🏅 {tier_name}: {len(clubs)} club")
            print(f"   Budget medio: €{avg_budget:.1f}M")
            print(f"   Overall medio: {avg_overall:.1f}")
            
            # Mostra esempi
            for club in clubs[:3]:  # Prime 3
                print(f"     • {club.nome}: €{club.budget_mln:.1f}M → Overall {club.get_team_overall()}")
            print()
    
    # Test distribuzione budget
    print("=" * 60)
    print("💸 ANALISI DISTRIBUZIONE BUDGET")
    print("=" * 60)
    
    # Esempi specifici
    examples = [
        ("Real Madrid", 900.0, League.EUROPA),
        ("Juventus", 300.0, League.SERIE_A),
        ("Parma", 30.0, League.SERIE_B),
        ("Pro Vercelli", 2.0, League.SERIE_C)
    ]
    
    for nome, budget_mln, campionato in examples:
        club = next((c for c in test_clubs if c.nome == nome), None)
        if club:
            budget_info = club.get_budget_breakdown()
            
            print(f"🔍 {nome} (€{budget_mln:.1f}M - {campionato.value}):")
            print(f"   Budget totale: €{club.budget:,}")
            print(f"   Trasferimenti: €{club.budget_trasferimenti:,} ({club.budget_trasferimenti/club.budget*100:.1f}%)")
            print(f"   Stipendi annui: €{club.budget_stipendi * 12:,} ({club.budget_stipendi*12/club.budget*100:.1f}%)")
            print(f"   Stipendi mensili: €{club.budget_stipendi:,}")
            print(f"   Rimanente: €{budget_info['budget_rimanente']:,}")
            print(f"   Livello: {club.get_club_tier()}")
            print(f"   Obiettivo: {club.obiettivo_principale}")
            print()
    
    # Test capacità di acquisto
    print("=" * 60)
    print("🛒 TEST CAPACITÀ DI ACQUISTO")
    print("=" * 60)
    
    # Simula alcuni acquisti
    test_transfers = [
        ("Giocatore Top", 100000000, 500000),    # €100M, €500K/mese
        ("Giocatore Buono", 30000000, 200000),   # €30M, €200K/mese
        ("Giocatore Medio", 10000000, 80000),    # €10M, €80K/mese
        ("Giovane Promessa", 2000000, 30000),    # €2M, €30K/mese
        ("Riserva", 500000, 15000)               # €500K, €15K/mese
    ]
    
    test_clubs_for_transfers = [
        ("Real Madrid", 900.0),
        ("Juventus", 300.0),
        ("Atalanta", 80.0),
        ("Parma", 30.0),
        ("Pro Vercelli", 2.0)
    ]
    
    for club_name, budget_mln in test_clubs_for_transfers:
        club = next((c for c in test_clubs if c.nome == club_name), None)
        if club:
            print(f"💰 {club_name} (€{budget_mln:.1f}M):")
            
            for transfer_name, cost, salary in test_transfers:
                can_afford = club.can_afford_player(cost, salary)
                status = "✅ SÌ" if can_afford else "❌ NO"
                print(f"   {transfer_name} (€{cost/1000000:.1f}M + €{salary/1000:.0f}K/mese): {status}")
            print()
    
    # Verifica obiettivi realistici
    print("=" * 60)
    print("🎯 VERIFICA OBIETTIVI REALISTICI")
    print("=" * 60)
    
    objective_analysis = {}
    for club in test_clubs:
        objective = club.obiettivo_principale
        if objective not in objective_analysis:
            objective_analysis[objective] = []
        objective_analysis[objective].append((club.nome, club.budget_mln, club.campionato.value))
    
    for objective, clubs in objective_analysis.items():
        print(f"🏆 {objective}:")
        clubs.sort(key=lambda x: x[1], reverse=True)  # Ordina per budget
        for nome, budget, campionato in clubs:
            print(f"   • {nome} (€{budget:.1f}M - {campionato})")
        print()
    
    print("✅ Test sistema budget completato con successo!")
    print("💡 Il sistema ora utilizza budget_mln per determinare:")
    print("   • Obiettivi stagionali realistici")
    print("   • Livello e tier del club")
    print("   • Distribuzione budget trasferimenti/stipendi")
    print("   • Forza dei giocatori generati")
    print("   • Capacità di acquisto nel mercato")
    
    return True

if __name__ == "__main__":
    success = test_budget_system()
    sys.exit(0 if success else 1)
