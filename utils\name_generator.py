"""
Generatore di nomi per giocatori e staff
"""

import random
from typing import List, Dict, <PERSON><PERSON>, Optional
from .json_loader import data_loader
import csv

class NameGenerator:
    """Classe per generare nomi realistici per giocatori e staff"""
    
    def __init__(self):
        self.names_data = data_loader.load_player_names()
        self.real_coaches = self._load_real_coaches()
        
        self.fallback_names = {} # Non più necessari con il nuovo JSON

    def _load_real_coaches(self) -> List[Tuple[str, str]]:
        """Carica i nomi degli allenatori reali da tutti i file CSV nella cartella data/."""
        coaches = []
        csv_files = [
            'data/serie_a.csv',
            'data/serie_b.csv',
            'data/serie_c_girone_A.csv',
            'data/serie_c_girone_B.csv',
            'data/serie_c_girone_C.csv',
            'data/altre_europa.csv'
        ]
        
        for file_path in csv_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    next(reader)  # Salta l'intestazione
                    for row in reader:
                        if len(row) > 9:  # Assicurati che la colonna dell'allenatore esista
                            full_name = row[9].strip()
                            if full_name:
                                parts = full_name.split(' ')
                                if len(parts) >= 2:
                                    first_name = parts[0]
                                    last_name = ' '.join(parts[1:])
                                    coaches.append((first_name, last_name))
                                else:
                                    coaches.append((full_name, full_name))
            except FileNotFoundError:
                print(f"ATTENZIONE: File '{file_path}' non trovato. Gli allenatori da questo file non verranno caricati.")
            except Exception as e:
                print(f"Errore durante il caricamento degli allenatori da '{file_path}': {e}")
        return list(set(coaches)) # Rimuovi duplicati

    def _get_names_list(self, nationality: str, name_type: str) -> List[str]:
        """Ottiene una lista di nomi o cognomi per una data nazionalità"""
        if self.names_data and nationality in self.names_data:
            return self.names_data[nationality].get(name_type, [])
        return [] # Restituisce una lista vuota se la nazionalità o il tipo di nome non sono trovati

    def generate_name_by_nationality(self, nationality: str) -> Tuple[str, str]:
        """Genera un nome basato sulla nazionalità"""
        first_names = self._get_names_list(nationality, "first_names")
        last_names = self._get_names_list(nationality, "last_names")

        if not first_names or not last_names:
            # Fallback a nomi italiani se la nazionalità non è trovata o non ha nomi
            first_names = self._get_names_list("Italia", "first_names")
            last_names = self._get_names_list("Italia", "last_names")
            if not first_names or not last_names:
                raise ValueError(f"Nessun nome disponibile per la nazionalità '{nationality}' o per 'Italia'.")

        nome = random.choice(first_names)
        cognome = random.choice(last_names)
        
        return nome, cognome
    
    def generate_italian_name(self) -> Tuple[str, str]:
        """Genera un nome italiano (nome, cognome)"""
        return self.generate_name_by_nationality("Italia")
    
    def generate_foreign_name(self, nationality: str = None) -> Tuple[str, str]:
        """Genera un nome straniero (nome, cognome)"""
        if nationality and nationality in self.names_data:
            return self.generate_name_by_nationality(nationality)
        else:
            # Se la nazionalità non è specificata o non valida, scegline una casuale (non italiana)
            available_nationalities = [nat for nat in self.names_data.keys() if nat != "Italia"]
            if not available_nationalities:
                return self.generate_italian_name() # Fallback se non ci sono nazionalità straniere
            
            random_nationality = random.choice(available_nationalities)
            return self.generate_name_by_nationality(random_nationality)
    
    def generate_random_name(self) -> Tuple[str, str]:
        """Genera un nome casuale (italiano o straniero)"""
        # Probabilità maggiore per nomi italiani
        if random.random() < 0.7:
            return self.generate_italian_name()
        else:
            return self.generate_foreign_name()
    
    def generate_unique_names(self, count: int, nationality: str = "Italia") -> List[Tuple[str, str]]:
        """Genera una lista di nomi unici"""
        names = set()
        attempts = 0
        max_attempts = count * 100 # Aumenta gli tentativi per garantire unicità
        
        while len(names) < count and attempts < max_attempts:
            name = self.generate_name_by_nationality(nationality)
            names.add(name)
            attempts += 1
        
        return list(names)
    
    def get_random_nationality(self) -> str:
        """Restituisce una nazionalità casuale basata sui dati disponibili"""
        if not self.names_data:
            return "Italia" # Fallback se non ci sono dati

        nationalities = list(self.names_data.keys())
        
        # Assegna pesi maggiori all'Italia se presente
        if "Italia" in nationalities:
            weights = [10 if nat == "Italia" else 1 for nat in nationalities]
            return random.choices(nationalities, weights=weights)[0]
        
        return random.choices(nationalities)[0] # Se Italia non c'è, scegli a caso tra le disponibili
    
    def generate_coach_name(self, nationality: str = "Italia") -> Tuple[str, str]:
        """Genera un nome per un allenatore (tendenzialmente più maturo)"""
        if nationality.lower() in ["italia", "italian", "italiano"] and self.real_coaches:
            # Seleziona un allenatore reale se disponibile
            return random.choice(self.real_coaches)
        else:
            # Altrimenti, genera un nome basato sulla nazionalità
            return self.generate_name_by_nationality(nationality)
    
    def generate_staff_name(self, role: str, nationality: str = "Italia") -> Tuple[str, str]:
        """Genera un nome per un membro dello staff"""
        if role.lower() == "allenatore":
            return self.generate_coach_name(nationality)
        else:
            return self.generate_name_by_nationality(nationality)

# Istanza globale del generatore di nomi
name_generator = NameGenerator()
