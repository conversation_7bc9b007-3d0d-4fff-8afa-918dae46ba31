"""
Dialog per la selezione del club all'inizio del gioco
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QListWidget, QListWidgetItem,
                            QComboBox, QTextEdit, QFrame, QMessageBox,
                            QProgressBar, QApplication)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap

from models.club import Club, League
from models.player import Position
from core.player_generator import player_generator
from utils.json_loader import data_loader
from utils.logger import setup_logger

class ClubLoadingThread(QThread):
    """Thread per caricare i dati dei club in background"""

    progress_updated = pyqtSignal(int)
    club_loaded = pyqtSignal(str, list)  # league_name, clubs_list
    loading_finished = pyqtSignal()
    status_updated = pyqtSignal(str)  # Nuovo segnale per aggiornare lo status

    def __init__(self):
        super().__init__()
        self.logger = setup_logger("club_loading")
    
    def run(self):
        """Carica tutti i club disponibili e genera le rose"""
        # Carica solo le squadre italiane per assegnare obiettivi
        leagues_data = {
            "Serie A": ("serie_a.csv", League.SERIE_A),
            "Serie B": ("serie_b.csv", League.SERIE_B),
            "Serie C Girone A": ("serie_c_girone_A.csv", League.SERIE_C),
            "Serie C Girone B": ("serie_c_girone_B.csv", League.SERIE_C),
            "Serie C Girone C": ("serie_c_girone_C.csv", League.SERIE_C),
        }

        total_leagues = len(leagues_data)
        all_clubs = []

        # Fase 1: Caricamento squadre
        self.status_updated.emit("Caricamento squadre...")
        current_progress = 0

        for league_name, (filename, league_enum) in leagues_data.items():
            self.logger.info(f"Caricamento {league_name}...")

            # Carica dati dal CSV
            teams_data = data_loader.load_csv(filename)

            if teams_data:
                clubs = self._create_clubs_from_data(teams_data, league_enum)
                all_clubs.extend(clubs)
                self.club_loaded.emit(league_name, clubs)
                self.logger.info(f"Caricate {len(clubs)} squadre per {league_name}")
            else:
                self.logger.warning(f"Nessun dato trovato per {league_name}")

            current_progress += 1
            progress_percentage = int((current_progress / total_leagues) * 50)  # Prima metà del progresso
            self.progress_updated.emit(progress_percentage)

        # Fase 2: Generazione rose
        self.status_updated.emit("Generazione rose giocatori...")
        self.logger.info(f"Inizio generazione rose per {len(all_clubs)} squadre...")

        for i, club in enumerate(all_clubs):
            self.status_updated.emit(f"Generazione rosa: {club.nome}...")

            try:
                # Genera la rosa per il club
                squad = player_generator.generate_squad(club)

                # Aggiungi giocatori al club
                for player in squad:
                    club.add_player(player)

                self.logger.info(f"Generata rosa per {club.nome}: {len(squad)} giocatori")

            except Exception as e:
                self.logger.error(f"Errore nella generazione della rosa per {club.nome}: {e}")

            # Aggiorna progresso (seconda metà)
            progress_percentage = 50 + int(((i + 1) / len(all_clubs)) * 50)
            self.progress_updated.emit(progress_percentage)

        self.status_updated.emit("Completato!")
        self.logger.info("Generazione rose completata per tutte le squadre")
        self.loading_finished.emit()
    
    def _create_clubs_from_data(self, teams_data: list, league: League) -> list:
        """Crea oggetti Club dai dati CSV"""
        clubs = []
        
        for i, team_data in enumerate(teams_data):
            try:
                # Usa sempre il budget_mln dal CSV se disponibile
                budget_mln = float(team_data.get('budget_mln', self._get_default_budget_mln(league)))
                budget = int(budget_mln * 1000000)  # Converti in euro
                
                # Capacità stadio
                capacita = int(team_data.get('capacita', team_data.get('capienza_stadio', '10000')).replace(',', '').replace('.', ''))

                if capacita == 0:
                    capacita = self._get_default_stadium_capacity(league)
                
                # Gestisce sia 'nome' che 'squadra' come nome del club
                nome_club = team_data.get('nome', team_data.get('squadra', 'Club Sconosciuto')).strip()
                citta_club = team_data.get('citta', nome_club).strip()
                stadio_nome = team_data.get('stadio', f"Stadio {nome_club}").strip()

                # Estrai statistiche tecniche dai CSV
                attacco = int(team_data.get('attacco', 70))
                difesa = int(team_data.get('difesa', 70))
                fisico = int(team_data.get('fisico', 70))
                velocita = int(team_data.get('velocità', team_data.get('velocita', 70)))

                club = Club(
                    club_id=i + 1,
                    nome=nome_club,
                    citta=citta_club,
                    campionato=league,
                    budget=budget,
                    stadio_nome=stadio_nome,
                    stadio_capacita=capacita,
                    budget_mln=budget_mln,
                    attacco=attacco,
                    difesa=difesa,
                    fisico=fisico,
                    velocita=velocita
                )
                
                clubs.append(club)
                
            except Exception as e:
                self.logger.error(f"Errore nella creazione del club {team_data}: {e}")
        
        return clubs
    
    def _get_base_budget(self, league: League) -> int:
        """Restituisce il budget base per campionato"""
        budgets = {
            League.SERIE_A: 30000000,  # 30M
            League.SERIE_B: 8000000,   # 8M
            League.SERIE_C: 2000000    # 2M
        }
        return budgets.get(league, 1000000)
    
    def _get_default_stadium_capacity(self, league: League) -> int:
        """Restituisce la capacità di default per campionato"""
        capacities = {
            League.SERIE_A: 25000,
            League.SERIE_B: 15000,
            League.SERIE_C: 8000,
        }
        return capacities.get(league, 5000)

    def _get_default_budget_mln(self, league: League) -> float:
        """Restituisce il budget di default in milioni per campionato"""
        default_budgets = {
            League.SERIE_A: 50.0,
            League.SERIE_B: 15.0,
            League.SERIE_C: 3.0,
        }
        return default_budgets.get(league, 5.0)

class ClubSelectionDialog(QDialog):
    """Dialog per selezionare il club con cui giocare"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = setup_logger("club_selection")
        self.all_clubs = {}  # {league_name: [clubs]}
        self.selected_club = None
        
        self.init_ui()
        self.start_loading()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        self.setWindowTitle("Selezione Club - Football President")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # Titolo
        title_label = QLabel("Seleziona il tuo Club")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Sottotitolo
        subtitle_label = QLabel("Scegli il club che vuoi dirigere come presidente")
        subtitle_label.setFont(QFont("Arial", 10))
        subtitle_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle_label)
        
        # Progress bar per il caricamento
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(True)
        layout.addWidget(self.progress_bar)

        # Etichetta di stato
        self.status_label = QLabel("Inizializzazione...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
        
        # Layout principale
        main_layout = QHBoxLayout()
        layout.addLayout(main_layout)
        
        # Pannello sinistro - Selezione campionato e club
        left_panel = QVBoxLayout()
        main_layout.addLayout(left_panel)
        
        # Selezione campionato
        league_label = QLabel("Campionato:")
        league_label.setFont(QFont("Arial", 12, QFont.Bold))
        left_panel.addWidget(league_label)
        
        self.league_combo = QComboBox()
        self.league_combo.currentTextChanged.connect(self.on_league_changed)
        left_panel.addWidget(self.league_combo)
        
        # Lista club
        clubs_label = QLabel("Club:")
        clubs_label.setFont(QFont("Arial", 12, QFont.Bold))
        left_panel.addWidget(clubs_label)
        
        self.clubs_list = QListWidget()
        self.clubs_list.itemSelectionChanged.connect(self.on_club_selected)
        left_panel.addWidget(self.clubs_list)
        
        # Pannello destro - Dettagli club
        right_panel = QVBoxLayout()
        main_layout.addLayout(right_panel)
        
        details_label = QLabel("Dettagli Club:")
        details_label.setFont(QFont("Arial", 12, QFont.Bold))
        right_panel.addWidget(details_label)
        
        self.details_frame = QFrame()
        self.details_frame.setFrameStyle(QFrame.StyledPanel)
        self.details_frame.setMinimumWidth(300)
        right_panel.addWidget(self.details_frame)
        
        self.setup_details_panel()
        
        # Pulsanti
        buttons_layout = QHBoxLayout()
        layout.addLayout(buttons_layout)

        buttons_layout.addStretch()

        cancel_btn = QPushButton("Annulla")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        self.start_btn = QPushButton("Inizia Partita")
        self.start_btn.clicked.connect(self.accept)
        self.start_btn.setEnabled(False)
        buttons_layout.addWidget(self.start_btn)
        
        # Inizialmente disabilita tutto tranne la progress bar
        self.set_controls_enabled(False)
    
    def setup_details_panel(self):
        """Configura il pannello dei dettagli"""
        layout = QVBoxLayout(self.details_frame)
        
        self.club_name_label = QLabel("Seleziona un club")
        self.club_name_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(self.club_name_label)
        
        self.club_info_text = QTextEdit()
        self.club_info_text.setReadOnly(True)
        self.club_info_text.setMaximumHeight(200)
        layout.addWidget(self.club_info_text)
        
        # Statistiche rosa
        self.squad_stats_label = QLabel("Rosa non generata")
        self.squad_stats_label.setFont(QFont("Arial", 10))
        layout.addWidget(self.squad_stats_label)
    
    def start_loading(self):
        """Avvia il caricamento dei club"""
        self.loading_thread = ClubLoadingThread()
        self.loading_thread.progress_updated.connect(self.progress_bar.setValue)
        self.loading_thread.club_loaded.connect(self.on_clubs_loaded)
        self.loading_thread.loading_finished.connect(self.on_loading_finished)
        self.loading_thread.status_updated.connect(self.status_label.setText)
        self.loading_thread.start()
    
    def on_clubs_loaded(self, league_name: str, clubs: list):
        """Chiamato quando i club di un campionato sono stati caricati"""
        self.all_clubs[league_name] = clubs
        self.league_combo.addItem(league_name)
    
    def on_loading_finished(self):
        """Chiamato quando il caricamento è terminato"""
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        self.set_controls_enabled(True)

        if self.league_combo.count() > 0:
            self.league_combo.setCurrentIndex(0)
            self.on_league_changed(self.league_combo.currentText())
    
    def set_controls_enabled(self, enabled: bool):
        """Abilita/disabilita i controlli"""
        self.league_combo.setEnabled(enabled)
        self.clubs_list.setEnabled(enabled)
    
    def on_league_changed(self, league_name: str):
        """Chiamato quando cambia la selezione del campionato"""
        self.clubs_list.clear()
        
        if league_name in self.all_clubs:
            clubs = self.all_clubs[league_name]
            for club in clubs:
                item = QListWidgetItem(f"{club.nome} ({club.citta})")
                item.setData(Qt.UserRole, club)
                self.clubs_list.addItem(item)
    
    def on_club_selected(self):
        """Chiamato quando viene selezionato un club"""
        current_item = self.clubs_list.currentItem()

        if current_item:
            club = current_item.data(Qt.UserRole)
            self.selected_club = club
            self.update_club_details(club)

            # Abilita il pulsante start (tutte le squadre hanno già una rosa)
            self.start_btn.setEnabled(True)
        else:
            self.selected_club = None
            self.start_btn.setEnabled(False)
    
    def update_club_details(self, club: Club):
        """Aggiorna i dettagli del club selezionato"""
        self.club_name_label.setText(club.nome)
        
        # Ottieni breakdown del budget
        budget_info = club.get_budget_breakdown()

        details_text = f"""
<b>Città:</b> {club.citta}<br>
<b>Campionato:</b> {club.campionato.value}<br>
<b>Livello Club:</b> {club.get_club_tier()}<br>
<b>Stadio:</b> {club.stadio_nome}<br>
<b>Capacità:</b> {club.stadio_capacita:,} spettatori<br>
<br>
<b>💰 BUDGET DETTAGLIATO:</b><br>
<b>Budget Totale:</b> €{club.budget_mln:.1f}M (€{club.budget:,})<br>
<b>Budget Trasferimenti:</b> €{club.budget_trasferimenti / 1000000:.1f}M<br>
<b>Budget Stipendi:</b> €{club.budget_stipendi / 1000:.0f}K/mese<br>
<b>Entrate Mensili:</b> €{club.entrate_mensili:,}<br>
<br>
<b>🎯 OBIETTIVI:</b><br>
<b>Principale:</b> {club.obiettivo_principale}<br>
"""
        
        if club.obiettivi_secondari:
            details_text += f"<b>Obiettivi secondari:</b><br>"
            for obj in club.obiettivi_secondari:
                details_text += f"• {obj}<br>"
        
        self.club_info_text.setHtml(details_text)
        
        # Aggiorna statistiche rosa
        self.update_squad_stats(club)
    
    def update_squad_stats(self, club: Club):
        """Aggiorna le statistiche della rosa"""
        if not club.giocatori:
            self.squad_stats_label.setText("Rosa in caricamento...")
            return

        # Conta giocatori per posizione
        positions_count = {pos: 0 for pos in Position}
        for player in club.giocatori:
            positions_count[player.posizione] += 1

        overall_avg = club.get_team_overall()
        total_salary = sum(p.stipendio for p in club.giocatori)

        stats_text = f"""Rosa: {len(club.giocatori)} giocatori
Portieri: {positions_count[Position.PORTIERE]}
Difensori: {positions_count[Position.DIFENSORE]}
Centrocampisti: {positions_count[Position.CENTROCAMPISTA]}
Attaccanti: {positions_count[Position.ATTACCANTE]}

Overall medio: {overall_avg}
Stipendi totali: €{total_salary:,}/mese"""

        self.squad_stats_label.setText(stats_text)
    

    
    def get_selected_club(self) -> Club:
        """Restituisce il club selezionato"""
        return self.selected_club
