"""
Sistema di gestione del calendario calcistico
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional

class SeasonPhase(Enum):
    """Fasi della stagione calcistica"""
    PREPARAZIONE = "Preparazione"
    CALCIOMERCATO_ESTIVO = "Calciomercato Estivo"
    CAMPIONATO_PRIMA_PARTE = "Prima Parte Campionato"
    CALCIOMERCATO_INVERNALE = "Calciomercato Invernale"
    CAMPIONATO_SECONDA_PARTE = "Seconda Parte Campionato"
    PLAYOFF_COPPA = "Playoff e Coppe"
    PAUSA_ESTIVA = "Pausa Estiva"

class GameCalendar:
    """Gestisce il calendario del gioco"""
    
    def __init__(self, start_year: int = 2024):
        self.start_year = start_year
        self.current_date = self._get_season_start_date()
        self.season_year = start_year
        
        # Date importanti della stagione
        self.season_dates = self._calculate_season_dates()
        
    def _get_season_start_date(self) -> datetime:
        """Restituisce la data di inizio stagione (1 luglio)"""
        return datetime(self.start_year, 7, 1)
    
    def _calculate_season_dates(self) -> Dict[str, datetime]:
        """Calcola tutte le date importanti della stagione"""
        year = self.season_year
        
        return {
            # Inizio stagione e preparazione
            "inizio_stagione": datetime(year, 7, 1),
            "inizio_preparazione": datetime(year, 7, 1),
            
            # Calciomercato estivo
            "apertura_mercato_estivo": datetime(year, 7, 1),
            "chiusura_mercato_estivo": datetime(year, 8, 31),
            
            # Inizio campionati
            "inizio_serie_a": datetime(year, 8, 20),
            "inizio_serie_b": datetime(year, 8, 27),
            "inizio_serie_c": datetime(year, 9, 3),
            
            # Calciomercato invernale
            "apertura_mercato_invernale": datetime(year + 1, 1, 2),
            "chiusura_mercato_invernale": datetime(year + 1, 2, 1),
            
            # Fine campionati
            "fine_serie_c": datetime(year + 1, 4, 28),
            "fine_serie_b": datetime(year + 1, 5, 10),
            "fine_serie_a": datetime(year + 1, 5, 24),
            
            # Playoff e coppe
            "inizio_playoff": datetime(year + 1, 5, 11),
            "fine_playoff": datetime(year + 1, 6, 15),
            
            # Fine stagione
            "fine_stagione": datetime(year + 1, 6, 30),
        }
    
    def get_current_date(self) -> datetime:
        """Restituisce la data corrente del gioco"""
        return self.current_date
    
    def get_current_date_string(self) -> str:
        """Restituisce la data corrente formattata"""
        return self.current_date.strftime("%d/%m/%Y")
    
    def get_current_phase(self) -> SeasonPhase:
        """Determina la fase corrente della stagione"""
        dates = self.season_dates
        current = self.current_date
        
        if current < dates["apertura_mercato_estivo"]:
            return SeasonPhase.PAUSA_ESTIVA
        elif current < dates["chiusura_mercato_estivo"]:
            return SeasonPhase.CALCIOMERCATO_ESTIVO
        elif current < dates["apertura_mercato_invernale"]:
            return SeasonPhase.CAMPIONATO_PRIMA_PARTE
        elif current < dates["chiusura_mercato_invernale"]:
            return SeasonPhase.CALCIOMERCATO_INVERNALE
        elif current < dates["fine_serie_a"]:
            return SeasonPhase.CAMPIONATO_SECONDA_PARTE
        elif current < dates["fine_playoff"]:
            return SeasonPhase.PLAYOFF_COPPA
        else:
            return SeasonPhase.PAUSA_ESTIVA
    
    def is_transfer_window_open(self) -> bool:
        """Verifica se il calciomercato è aperto"""
        phase = self.get_current_phase()
        return phase in [SeasonPhase.CALCIOMERCATO_ESTIVO, SeasonPhase.CALCIOMERCATO_INVERNALE]
    
    def is_season_active(self) -> bool:
        """Verifica se la stagione è attiva (campionati in corso)"""
        phase = self.get_current_phase()
        return phase in [
            SeasonPhase.CAMPIONATO_PRIMA_PARTE,
            SeasonPhase.CAMPIONATO_SECONDA_PARTE,
            SeasonPhase.PLAYOFF_COPPA
        ]
    
    def get_phase_info(self) -> Dict[str, str]:
        """Restituisce informazioni dettagliate sulla fase corrente"""
        phase = self.get_current_phase()
        dates = self.season_dates
        current = self.current_date
        
        info = {
            "fase": phase.value,
            "descrizione": "",
            "prossimo_evento": "",
            "data_prossimo_evento": ""
        }
        
        if phase == SeasonPhase.CALCIOMERCATO_ESTIVO:
            info["descrizione"] = "Il calciomercato estivo è aperto"
            info["prossimo_evento"] = "Chiusura mercato estivo"
            info["data_prossimo_evento"] = dates["chiusura_mercato_estivo"].strftime("%d/%m/%Y")
            
        elif phase == SeasonPhase.CAMPIONATO_PRIMA_PARTE:
            info["descrizione"] = "Campionati in corso - Prima parte"
            info["prossimo_evento"] = "Apertura mercato invernale"
            info["data_prossimo_evento"] = dates["apertura_mercato_invernale"].strftime("%d/%m/%Y")
            
        elif phase == SeasonPhase.CALCIOMERCATO_INVERNALE:
            info["descrizione"] = "Il calciomercato invernale è aperto"
            info["prossimo_evento"] = "Chiusura mercato invernale"
            info["data_prossimo_evento"] = dates["chiusura_mercato_invernale"].strftime("%d/%m/%Y")
            
        elif phase == SeasonPhase.CAMPIONATO_SECONDA_PARTE:
            info["descrizione"] = "Campionati in corso - Seconda parte"
            info["prossimo_evento"] = "Fine campionati"
            info["data_prossimo_evento"] = dates["fine_serie_a"].strftime("%d/%m/%Y")
            
        elif phase == SeasonPhase.PLAYOFF_COPPA:
            info["descrizione"] = "Playoff e finali di coppa"
            info["prossimo_evento"] = "Fine stagione"
            info["data_prossimo_evento"] = dates["fine_stagione"].strftime("%d/%m/%Y")
            
        else:  # PAUSA_ESTIVA
            info["descrizione"] = "Pausa estiva"
            info["prossimo_evento"] = "Inizio nuova stagione"
            info["data_prossimo_evento"] = dates["inizio_stagione"].strftime("%d/%m/%Y")
        
        return info
    
    def get_days_until_next_event(self) -> int:
        """Restituisce i giorni fino al prossimo evento importante"""
        phase = self.get_current_phase()
        dates = self.season_dates
        current = self.current_date
        
        next_event_date = None
        
        if phase == SeasonPhase.CALCIOMERCATO_ESTIVO:
            next_event_date = dates["chiusura_mercato_estivo"]
        elif phase == SeasonPhase.CAMPIONATO_PRIMA_PARTE:
            next_event_date = dates["apertura_mercato_invernale"]
        elif phase == SeasonPhase.CALCIOMERCATO_INVERNALE:
            next_event_date = dates["chiusura_mercato_invernale"]
        elif phase == SeasonPhase.CAMPIONATO_SECONDA_PARTE:
            next_event_date = dates["fine_serie_a"]
        elif phase == SeasonPhase.PLAYOFF_COPPA:
            next_event_date = dates["fine_stagione"]
        else:  # PAUSA_ESTIVA
            next_event_date = dates["inizio_stagione"]
        
        if next_event_date:
            delta = next_event_date - current
            return max(0, delta.days)
        
        return 0
    
    def advance_days(self, days: int = 1):
        """Avanza il calendario di un numero di giorni"""
        self.current_date += timedelta(days=days)
        
        # Controlla se è iniziata una nuova stagione
        if self.current_date >= datetime(self.season_year + 1, 7, 1):
            self.season_year += 1
            self.season_dates = self._calculate_season_dates()
    
    def advance_to_next_event(self):
        """Avanza direttamente al prossimo evento importante"""
        phase = self.get_current_phase()
        dates = self.season_dates
        
        if phase == SeasonPhase.CALCIOMERCATO_ESTIVO:
            self.current_date = dates["inizio_serie_a"]
        elif phase == SeasonPhase.CAMPIONATO_PRIMA_PARTE:
            self.current_date = dates["apertura_mercato_invernale"]
        elif phase == SeasonPhase.CALCIOMERCATO_INVERNALE:
            self.current_date = dates["chiusura_mercato_invernale"]
        elif phase == SeasonPhase.CAMPIONATO_SECONDA_PARTE:
            self.current_date = dates["fine_serie_a"]
        elif phase == SeasonPhase.PLAYOFF_COPPA:
            self.current_date = dates["fine_stagione"]
        else:  # PAUSA_ESTIVA
            self.current_date = dates["inizio_stagione"]
    
    def get_season_info(self) -> Dict[str, str]:
        """Restituisce informazioni sulla stagione corrente"""
        return {
            "stagione": f"{self.season_year}/{self.season_year + 1}",
            "anno_inizio": str(self.season_year),
            "anno_fine": str(self.season_year + 1)
        }

# Istanza globale del calendario
game_calendar = GameCalendar()
