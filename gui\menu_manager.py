"""
Gestore dei menu per la finestra principale
"""

from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal

class MenuManager(QObject):
    """Gestore dei menu dell'applicazione"""
    
    # Segnali per comunicare con la finestra principale
    new_game_requested = pyqtSignal()
    load_game_requested = pyqtSignal()
    save_game_requested = pyqtSignal()
    fullscreen_toggled = pyqtSignal()
    about_requested = pyqtSignal()
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.setup_menus()
    
    def setup_menus(self):
        """Configura tutti i menu"""
        menubar = self.main_window.menuBar()
        
        # Menu File
        self.create_file_menu(menubar)
        
        # Menu Visualizza
        self.create_view_menu(menubar)
        
        # Menu Aiuto
        self.create_help_menu(menubar)
    
    def create_file_menu(self, menubar):
        """Crea il menu File"""
        file_menu = menubar.addMenu('File')
        
        new_game_action = file_menu.addAction('Nuova Partita')
        new_game_action.setShortcut('Ctrl+N')
        new_game_action.triggered.connect(self.new_game)
        
        load_game_action = file_menu.addAction('Carica Partita')
        load_game_action.setShortcut('Ctrl+O')
        load_game_action.triggered.connect(self.load_game)
        
        save_game_action = file_menu.addAction('Salva Partita')
        save_game_action.setShortcut('Ctrl+S')
        save_game_action.triggered.connect(self.save_game)
        
        file_menu.addSeparator()
        
        exit_action = file_menu.addAction('Esci')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.main_window.close)
    
    def create_view_menu(self, menubar):
        """Crea il menu Visualizza"""
        view_menu = menubar.addMenu('Visualizza')
        
        fullscreen_action = view_menu.addAction('Schermo Intero')
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        
        # Aggiungi altre opzioni di visualizzazione qui se necessario
    
    def create_help_menu(self, menubar):
        """Crea il menu Aiuto"""
        help_menu = menubar.addMenu('Aiuto')
        
        about_action = help_menu.addAction('Informazioni')
        about_action.triggered.connect(self.show_about)
        
        # Aggiungi altre opzioni di aiuto qui se necessario
    
    def new_game(self):
        """Gestisce la richiesta di nuova partita"""
        reply = QMessageBox.question(
            self.main_window, 'Nuova Partita',
            'Sei sicuro di voler iniziare una nuova partita?\n'
            'I progressi attuali andranno persi.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.new_game_requested.emit()
    
    def load_game(self):
        """Gestisce il caricamento di una partita"""
        # Per ora mostra un messaggio placeholder
        QMessageBox.information(
            self.main_window, 
            "Carica Partita", 
            "Funzione non ancora implementata.\n\n"
            "Sarà disponibile nelle prossime versioni."
        )
        self.load_game_requested.emit()
    
    def save_game(self):
        """Gestisce il salvataggio della partita"""
        # Per ora mostra un messaggio placeholder
        QMessageBox.information(
            self.main_window, 
            "Salva Partita", 
            "Funzione non ancora implementata.\n\n"
            "Sarà disponibile nelle prossime versioni."
        )
        self.save_game_requested.emit()
    
    def toggle_fullscreen(self):
        """Attiva/disattiva la modalità schermo intero"""
        if self.main_window.isFullScreen():
            self.main_window.showNormal()
        else:
            self.main_window.showFullScreen()
        
        self.fullscreen_toggled.emit()
    
    def show_about(self):
        """Mostra le informazioni sull'applicazione"""
        QMessageBox.about(
            self.main_window, 
            "Informazioni su Football President",
            "<h3>Football President v1.0</h3>"
            "<p>Un gioco manageriale di calcio completo dove interpreti "
            "il presidente di un club italiano.</p>"
            "<p><b>Caratteristiche:</b></p>"
            "<ul>"
            "<li>Gestione completa del club</li>"
            "<li>Sistema di calciomercato realistico</li>"
            "<li>Calendario stagionale dinamico</li>"
            "<li>Obiettivi basati sui dati reali</li>"
            "<li>256 squadre italiane ed europee</li>"
            "</ul>"
            "<p><b>Sviluppato con:</b> Python e PyQt5</p>"
            "<p><b>Anno:</b> 2024</p>"
        )
        self.about_requested.emit()
