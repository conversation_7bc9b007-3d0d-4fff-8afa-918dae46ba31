"""
Sistema di logging per l'applicazione
"""

import logging
import os
from datetime import datetime
from typing import Optional

def setup_logger(name: str = "football_president", 
                level: int = logging.INFO,
                log_file: Optional[str] = None) -> logging.Logger:
    """
    Configura e restituisce un logger per l'applicazione
    
    Args:
        name: Nome del logger
        level: Livello di logging
        log_file: File di log (opzionale)
    
    Returns:
        Logger configurato
    """
    
    # Crea il logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Evita duplicazione degli handler
    if logger.handlers:
        return logger
    
    # Formato dei messaggi
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Handler per console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Handler per file (se specificato)
    if log_file:
        # Crea la directory dei log se non esiste
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    else:
        # File di log di default
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        today = datetime.now().strftime("%Y-%m-%d")
        default_log_file = os.path.join(log_dir, f"football_president_{today}.log")
        
        file_handler = logging.FileHandler(default_log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

class GameLogger:
    """Logger specifico per eventi di gioco"""
    
    def __init__(self):
        self.logger = setup_logger("game_events", logging.INFO)
        self.match_logger = setup_logger("matches", logging.INFO, "logs/matches.log")
        self.transfer_logger = setup_logger("transfers", logging.INFO, "logs/transfers.log")
        self.finance_logger = setup_logger("finances", logging.INFO, "logs/finances.log")
    
    def log_match(self, match_info: str):
        """Log eventi delle partite"""
        self.match_logger.info(match_info)
    
    def log_transfer(self, transfer_info: str):
        """Log trasferimenti"""
        self.transfer_logger.info(transfer_info)
    
    def log_finance(self, finance_info: str):
        """Log eventi finanziari"""
        self.finance_logger.info(finance_info)
    
    def log_game_event(self, event_info: str):
        """Log eventi generali del gioco"""
        self.logger.info(event_info)

# Istanza globale del game logger
game_logger = GameLogger()
