# Football President - Gioco Manageriale di Calcio

Un gioco manageriale di calcio sviluppato in Python con PyQt5, dove il giocatore interpreta il ruolo del presidente di un club italiano dalla Serie A alla Serie C.

## 🎯 Caratteristiche Principali

- **Ruolo del Presidente**: Gestisci il club come un vero presidente
- **Campionati Italiani**: Serie A, Serie B e Serie C con squadre reali
- **Gestione Completa**: Staff, giocatori, finanze, stadio e infrastrutture
- **Simulazione Realistica**: Partite, trasferimenti e eventi casuali
- **Interfaccia Moderna**: GUI sviluppata con PyQt5

## 🏗️ Struttura del Progetto

```
football_president/
├── core/                   # Logica del gioco
│   ├── game_loop.py
│   ├── scheduler.py
│   ├── finances.py
│   ├── transfers.py
│   ├── player_generator.py
│   ├── match_simulator.py
│   └── team_manager.py
├── data/                   # Dati statici
│   ├── serie_a.csv
│   ├── serie_b.csv
│   ├── serie_c_girone_*.csv
│   ├── player_names.json
│   └── stadiums.json
├── gui/                    # Interfaccia PyQt5
│   ├── main_window.py
│   ├── club_selection.py
│   ├── dashboard.py
│   └── modals/
├── models/                 # Oggetti del gioco
│   ├── club.py
│   ├── player.py
│   ├── staff.py
│   ├── match.py
│   └── season.py
├── utils/                  # Utilità
│   ├── json_loader.py
│   ├── name_generator.py
│   ├── config.py
│   └── logger.py
└── main.py                 # Entry point
```

## 🚀 Installazione e Avvio

### Prerequisiti

- Python 3.8 o superiore
- PyQt5
- pandas (opzionale, per gestione dati avanzata)

### Installazione

1. Clona o scarica il progetto
2. Installa le dipendenze:

```bash
pip install PyQt5 pandas
```

3. Avvia il gioco:

```bash
python main.py
```

## 🎮 Come Giocare

1. **Selezione Club**: All'avvio, scegli il club che vuoi dirigere
2. **Genera Rosa**: Crea automaticamente una rosa di giocatori per il tuo club
3. **Gestione**: Usa la dashboard per monitorare:
   - Budget e finanze
   - Rosa e statistiche giocatori
   - Staff tecnico
   - Prossime partite
   - Notizie e eventi

## 🔧 Funzionalità Implementate

### ✅ Completate
- [x] Struttura base del progetto
- [x] Modelli per Club, Player, Staff, Match, Season
- [x] Generatore di giocatori realistici
- [x] Interfaccia di selezione club
- [x] Dashboard principale
- [x] Sistema di configurazione
- [x] Sistema di logging
- [x] Caricamento dati da CSV

### 🚧 In Sviluppo
- [ ] Simulazione partite complete
- [ ] Sistema di trasferimenti
- [ ] Gestione staff tecnico
- [ ] Calendario e fixture
- [ ] Sistema finanziario avanzato
- [ ] Eventi casuali
- [ ] Salvataggio/Caricamento partite

### 📋 Pianificate
- [ ] Modalità multiplayer
- [ ] Editor squadre personalizzate
- [ ] Statistiche avanzate
- [ ] Sistema di achievement
- [ ] Modalità carriera estesa

## 🎯 Obiettivi del Gioco

Come presidente del club, dovrai:

- **Gestire le Finanze**: Bilancia entrate e uscite
- **Assumere Staff**: Allenatore, direttore sportivo, staff tecnico
- **Trasferimenti**: Acquista e vendi giocatori
- **Infrastrutture**: Migliora stadio e centro sportivo
- **Obiettivi**: Raggiungi gli obiettivi stagionali
- **Tifosi**: Mantieni alta la soddisfazione dei supporter

## 🔧 Configurazione

Il file `config.json` permette di personalizzare:

- Difficoltà del gioco
- Velocità di simulazione
- Impostazioni grafiche
- Percorsi dei file
- Opzioni di gameplay

## 📊 Dati

Il gioco utilizza dati reali delle squadre italiane:

- **Serie A**: 20 squadre
- **Serie B**: 20 squadre  
- **Serie C**: 60 squadre (3 gironi da 20)

I giocatori sono generati proceduralmente con statistiche realistiche basate su:
- Età e posizione
- Livello del campionato
- Nazionalità
- Potenziale di crescita

## 🐛 Debug e Logging

Il sistema di logging registra:
- Eventi di gioco in `logs/football_president_YYYY-MM-DD.log`
- Partite in `logs/matches.log`
- Trasferimenti in `logs/transfers.log`
- Finanze in `logs/finances.log`

## 🤝 Contribuire

Il progetto è aperto a contributi! Aree dove puoi aiutare:

1. **Bilanciamento**: Migliorare la simulazione delle partite
2. **UI/UX**: Perfezionare l'interfaccia utente
3. **Dati**: Aggiungere più squadre e campionati
4. **Features**: Implementare nuove funzionalità
5. **Testing**: Trovare e correggere bug

## 📝 Licenza

Progetto sviluppato per scopi educativi e di intrattenimento.

## 🎮 Crediti

Sviluppato con:
- **Python 3.x**
- **PyQt5** per l'interfaccia grafica
- **pandas** per la gestione dati
- Dati squadre italiane da fonti pubbliche

---

**Buon divertimento come presidente del tuo club! ⚽🏆**
