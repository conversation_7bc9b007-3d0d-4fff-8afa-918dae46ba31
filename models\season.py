"""
Modello per rappresentare una stagione calcistica
"""

from typing import List, Dict, Optional
from datetime import datetime, timedelta
from enum import Enum
import random

from .club import Club, League
from .match import Match, MatchType

class SeasonStatus(Enum):
    """Stato della stagione"""
    NON_INIZIATA = "non_iniziata"
    IN_CORSO = "in_corso"
    FINITA = "finita"
    SOSPESA = "sospesa"

class Season:
    """Classe che rappresenta una stagione calcistica"""
    
    def __init__(self,
                 season_id: int,
                 anno_inizio: int,
                 campionato: League,
                 squadre: List[Club]):
        
        self.id = season_id
        self.anno_inizio = anno_inizio
        self.anno_fine = anno_inizio + 1
        self.campionato = campionato
        self.squadre = squadre
        
        # Calendario
        self.partite: List[Match] = []
        self.giornata_corrente = 1
        self.giornate_totali = (len(squadre) - 1) * 2  # Andata e ritorno
        
        # Date importanti
        self.data_inizio = datetime(anno_inizio, 8, 20)  # Inizio campionato
        self.data_fine = datetime(anno_inizio + 1, 5, 30)  # Fine campionato
        self.data_mercato_estivo = (datetime(anno_inizio, 7, 1), datetime(anno_inizio, 8, 31))
        self.data_mercato_invernale = (datetime(anno_inizio + 1, 1, 1), datetime(anno_inizio + 1, 1, 31))
        
        # Stato
        self.status = SeasonStatus.NON_INIZIATA
        self.data_corrente = self.data_inizio
        
        # Classifiche
        self.classifica: List[Dict] = []
        
        # Statistiche
        self.capocannoniere: Optional[str] = None
        self.miglior_assist_man: Optional[str] = None
        
        # Genera il calendario
        self._generate_calendar()
    
    def _generate_calendar(self):
        """Genera il calendario del campionato"""
        num_squadre = len(self.squadre)
        if num_squadre % 2 != 0:
            raise ValueError("Il numero di squadre deve essere pari")
        
        # Algoritmo round-robin per generare il calendario
        teams = list(range(num_squadre))
        fixtures = []
        
        for round_num in range(num_squadre - 1):
            round_fixtures = []
            for i in range(num_squadre // 2):
                home_team = teams[i]
                away_team = teams[num_squadre - 1 - i]
                round_fixtures.append((home_team, away_team))
            
            fixtures.append(round_fixtures)
            
            # Ruota le squadre (tranne la prima)
            teams = [teams[0]] + [teams[-1]] + teams[1:-1]
        
        # Crea le partite per andata e ritorno
        match_id = 1
        current_date = self.data_inizio
        
        # Andata
        for giornata, round_fixtures in enumerate(fixtures, 1):
            for home_idx, away_idx in round_fixtures:
                match = Match(
                    match_id=match_id,
                    squadra_casa=self.squadre[home_idx],
                    squadra_trasferta=self.squadre[away_idx],
                    data=current_date,
                    tipo=MatchType.CAMPIONATO,
                    giornata=giornata
                )
                self.partite.append(match)
                match_id += 1
            
            current_date += timedelta(days=7)  # Una settimana tra le giornate
        
        # Ritorno (squadre invertite)
        for giornata, round_fixtures in enumerate(fixtures, num_squadre):
            for home_idx, away_idx in round_fixtures:
                match = Match(
                    match_id=match_id,
                    squadra_casa=self.squadre[away_idx],  # Invertito
                    squadra_trasferta=self.squadre[home_idx],  # Invertito
                    data=current_date,
                    tipo=MatchType.CAMPIONATO,
                    giornata=giornata
                )
                self.partite.append(match)
                match_id += 1
            
            current_date += timedelta(days=7)
    
    def get_matches_by_gameweek(self, giornata: int) -> List[Match]:
        """Restituisce le partite di una giornata specifica"""
        return [m for m in self.partite if m.giornata == giornata]
    
    def get_club_matches(self, club: Club) -> List[Match]:
        """Restituisce tutte le partite di un club"""
        return [m for m in self.partite 
                if m.squadra_casa == club or m.squadra_trasferta == club]
    
    def get_next_matches(self, club: Club, num_matches: int = 5) -> List[Match]:
        """Restituisce le prossime partite di un club"""
        club_matches = self.get_club_matches(club)
        upcoming = [m for m in club_matches if m.data >= self.data_corrente]
        return sorted(upcoming, key=lambda x: x.data)[:num_matches]
    
    def simulate_gameweek(self, giornata: int) -> List[Match]:
        """Simula tutte le partite di una giornata"""
        matches = self.get_matches_by_gameweek(giornata)
        results = []
        
        for match in matches:
            if match.status.value == "programmata":
                match.simulate_match()
                results.append(match)
        
        # Aggiorna la classifica dopo la giornata
        self._update_standings()
        
        return results
    
    def simulate_to_date(self, target_date: datetime) -> List[Match]:
        """Simula tutte le partite fino a una data specifica"""
        results = []
        
        for match in self.partite:
            if match.data <= target_date and match.status.value == "programmata":
                match.simulate_match()
                results.append(match)
        
        self._update_standings()
        return results
    
    def advance_week(self):
        """Avanza di una settimana nella stagione"""
        self.data_corrente += timedelta(days=7)
        
        # Simula le partite della settimana
        weekly_matches = [m for m in self.partite 
                         if m.data <= self.data_corrente and m.status.value == "programmata"]
        
        for match in weekly_matches:
            match.simulate_match()
        
        if weekly_matches:
            self._update_standings()
        
        # Aggiorna lo stato della stagione
        if self.data_corrente >= self.data_fine:
            self.status = SeasonStatus.FINITA
        elif self.status == SeasonStatus.NON_INIZIATA and self.data_corrente >= self.data_inizio:
            self.status = SeasonStatus.IN_CORSO
    
    def _update_standings(self):
        """Aggiorna la classifica"""
        standings = []
        
        for club in self.squadre:
            club_data = {
                'club': club,
                'punti': club.punti,
                'partite_giocate': club.partite_giocate,
                'vittorie': club.vittorie,
                'pareggi': club.pareggi,
                'sconfitte': club.sconfitte,
                'gol_fatti': club.gol_fatti,
                'gol_subiti': club.gol_subiti,
                'differenza_reti': club.gol_fatti - club.gol_subiti
            }
            standings.append(club_data)
        
        # Ordina per punti, poi differenza reti, poi gol fatti
        standings.sort(key=lambda x: (-x['punti'], -x['differenza_reti'], -x['gol_fatti']))
        
        # Aggiungi posizione
        for i, club_data in enumerate(standings, 1):
            club_data['posizione'] = i
        
        self.classifica = standings
    
    def get_standings(self) -> List[Dict]:
        """Restituisce la classifica attuale"""
        if not self.classifica:
            self._update_standings()
        return self.classifica
    
    def get_club_position(self, club: Club) -> int:
        """Restituisce la posizione di un club in classifica"""
        standings = self.get_standings()
        for club_data in standings:
            if club_data['club'] == club:
                return club_data['posizione']
        return len(self.squadre)  # Ultima posizione se non trovato
    
    def is_transfer_window_open(self) -> bool:
        """Verifica se il mercato è aperto"""
        current_date = self.data_corrente.date()
        
        # Mercato estivo
        if (self.data_mercato_estivo[0].date() <= current_date <= 
            self.data_mercato_estivo[1].date()):
            return True
        
        # Mercato invernale
        if (self.data_mercato_invernale[0].date() <= current_date <= 
            self.data_mercato_invernale[1].date()):
            return True
        
        return False
    
    def get_top_scorers(self, limit: int = 10) -> List[Dict]:
        """Restituisce la classifica marcatori"""
        all_players = []
        for club in self.squadre:
            for player in club.giocatori:
                if player.gol > 0:
                    all_players.append({
                        'player': player,
                        'club': club,
                        'gol': player.gol,
                        'presenze': player.presenze
                    })
        
        # Ordina per gol, poi per presenze (meno presenze = meglio)
        all_players.sort(key=lambda x: (-x['gol'], x['presenze']))
        return all_players[:limit]
    
    def get_season_summary(self) -> Dict:
        """Restituisce un riassunto della stagione"""
        if self.status != SeasonStatus.FINITA:
            return {"error": "Stagione non ancora terminata"}
        
        standings = self.get_standings()
        top_scorers = self.get_top_scorers(3)
        
        # Determina promozioni e retrocessioni
        promotions = []
        relegations = []
        
        if self.campionato == League.SERIE_B:
            promotions = standings[:3]  # Prime 3 promosse in Serie A
            relegations = standings[-4:]  # Ultime 4 retrocesse in Serie C
        elif self.campionato == League.SERIE_C:
            promotions = standings[:1]  # Prima promossa in Serie B
            relegations = standings[-4:]  # Ultime 4 retrocesse
        elif self.campionato == League.SERIE_A:
            relegations = standings[-3:]  # Ultime 3 retrocesse in Serie B
        
        return {
            'campione': standings[0]['club'] if standings else None,
            'classifica_finale': standings,
            'capocannoniere': top_scorers[0] if top_scorers else None,
            'promozioni': promotions,
            'retrocessioni': relegations,
            'partite_totali': len(self.partite),
            'gol_totali': sum(m.gol_casa + m.gol_trasferta for m in self.partite if m.status.value == "finita")
        }
    
    def __str__(self):
        return f"Stagione {self.anno_inizio}/{self.anno_fine} - {self.campionato.value}"
