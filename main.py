#!/usr/bin/env python3
"""
Football President - Gioco Manageriale di Calcio
Entry point principale dell'applicazione
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import MainWindow
from utils.config import Config
from utils.logger import setup_logger

def main():
    """Funzione principale per avviare l'applicazione"""
    
    # Setup del logger
    logger = setup_logger()
    logger.info("Avvio Football President...")
    
    # Carica configurazione
    config = Config()
    
    # Crea l'applicazione Qt
    app = QApplication(sys.argv)
    app.setApplicationName("Football President")
    app.setApplicationVersion("1.0.0")
    
    # Imposta lo stile dell'applicazione
    app.setStyle('Fusion')
    
    # Crea e mostra la finestra principale
    main_window = MainWindow()
    main_window.show()
    
    logger.info("Applicazione avviata con successo")
    
    # Avvia il loop dell'applicazione
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
