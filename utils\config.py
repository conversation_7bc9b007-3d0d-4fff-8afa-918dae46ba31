"""
Configurazione dell'applicazione
"""

import os
import json
from typing import Dict, Any

class Config:
    """Classe per gestire la configurazione dell'applicazione"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_data = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Carica la configurazione da file"""
        default_config = {
            "game": {
                "starting_year": 2024,
                "simulation_speed": 1,
                "auto_save": True,
                "auto_save_interval": 5  # minuti
            },
            "database": {
                "path": "data/",
                "backup_enabled": True,
                "backup_interval": 24  # ore
            },
            "ui": {
                "theme": "default",
                "language": "it",
                "window_width": 1200,
                "window_height": 800,
                "fullscreen": False
            },
            "gameplay": {
                "difficulty": "normal",  # easy, normal, hard
                "realistic_transfers": True,
                "injuries_enabled": True,
                "weather_effects": True
            },
            "paths": {
                "data_dir": "data/",
                "saves_dir": "saves/",
                "logs_dir": "logs/",
                "temp_dir": "temp/"
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Merge con la configurazione di default
                    return self._merge_configs(default_config, loaded_config)
            except (json.JSONDecodeError, IOError):
                print(f"Errore nel caricamento di {self.config_file}, uso configurazione di default")
        
        return default_config
    
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """Unisce la configurazione di default con quella caricata"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get(self, key: str, default: Any = None) -> Any:
        """Ottiene un valore di configurazione usando la notazione dot"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Imposta un valore di configurazione usando la notazione dot"""
        keys = key.split('.')
        config = self.config_data
        
        # Naviga fino al penultimo livello
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Imposta il valore
        config[keys[-1]] = value
    
    def save(self):
        """Salva la configurazione su file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            print(f"Errore nel salvataggio della configurazione: {e}")
    
    def create_directories(self):
        """Crea le directory necessarie"""
        directories = [
            self.get('paths.data_dir'),
            self.get('paths.saves_dir'),
            self.get('paths.logs_dir'),
            self.get('paths.temp_dir')
        ]
        
        for directory in directories:
            if directory and not os.path.exists(directory):
                try:
                    os.makedirs(directory, exist_ok=True)
                except OSError as e:
                    print(f"Errore nella creazione della directory {directory}: {e}")
    
    # Proprietà di accesso rapido
    @property
    def starting_year(self) -> int:
        return self.get('game.starting_year', 2024)
    
    @property
    def data_dir(self) -> str:
        return self.get('paths.data_dir', 'data/')
    
    @property
    def saves_dir(self) -> str:
        return self.get('paths.saves_dir', 'saves/')
    
    @property
    def window_size(self) -> tuple:
        return (
            self.get('ui.window_width', 1200),
            self.get('ui.window_height', 800)
        )
    
    @property
    def difficulty(self) -> str:
        return self.get('gameplay.difficulty', 'normal')
    
    @property
    def language(self) -> str:
        return self.get('ui.language', 'it')

# Istanza globale della configurazione
config = Config()
