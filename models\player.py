"""
Modello per rappresentare un giocatore di calcio
"""

from dataclasses import dataclass
from typing import Dict, Optional
from enum import Enum
import random

class Position(Enum):
    """Posizioni dei giocatori"""
    PORTIERE = "P"
    DIFENSORE = "D"
    CENTROCAMPISTA = "C"
    ATTACCANTE = "A"

class PlayerStatus(Enum):
    """Stato del giocatore"""
    DISPONIBILE = "disponibile"
    INFORTUNATO = "infortunato"
    SQUALIFICATO = "squalificato"
    IN_PRESTITO = "in_prestito"

@dataclass
class PlayerStats:
    """Statistiche del giocatore"""
    tecnica: int = 50
    velocita: int = 50
    resistenza: int = 50
    forza: int = 50
    mentalita: int = 50
    esperienza: int = 50
    
    def overall(self) -> int:
        """Calcola il valore complessivo del giocatore"""
        return int((self.tecnica + self.velocita + self.resistenza + 
                   self.forza + self.mentalita + self.esperienza) / 6)

class Player:
    """Classe che rappresenta un giocatore"""
    
    def __init__(self, 
                 player_id: int,
                 nome: str,
                 cognome: str,
                 eta: int,
                 posizione: Position,
                 nazionalita: str = "Italia",
                 valore_mercato: int = 100000,
                 stipendio: int = 10000,
                 contratto_scadenza: int = 2025):
        
        self.id = player_id
        self.nome = nome
        self.cognome = cognome
        self.eta = eta
        self.posizione = posizione
        self.nazionalita = nazionalita
        self.valore_mercato = valore_mercato
        self.stipendio = stipendio
        self.contratto_scadenza = contratto_scadenza
        
        # Statistiche
        self.stats = PlayerStats()
        self._generate_stats_by_age_and_position()
        
        # Stato
        self.status = PlayerStatus.DISPONIBILE
        self.giorni_infortunio = 0
        self.giornate_squalifica = 0
        
        # Statistiche stagionali
        self.presenze = 0
        self.gol = 0
        self.assist = 0
        self.cartellini_gialli = 0
        self.cartellini_rossi = 0
        
        # Morale e forma
        self.morale = random.randint(70, 90)
        self.forma = random.randint(70, 90)
    
    def _generate_stats_by_age_and_position(self):
        """Genera statistiche basate su età e posizione"""
        base_stats = self._get_base_stats_by_position()
        age_modifier = self._get_age_modifier()
        
        for stat_name, base_value in base_stats.items():
            modified_value = int(base_value * age_modifier)
            modified_value = max(20, min(99, modified_value))  # Limita tra 20 e 99
            setattr(self.stats, stat_name, modified_value)
    
    def _get_base_stats_by_position(self) -> Dict[str, int]:
        """Restituisce statistiche base per posizione"""
        if self.posizione == Position.PORTIERE:
            return {
                'tecnica': random.randint(40, 70),
                'velocita': random.randint(30, 60),
                'resistenza': random.randint(60, 85),
                'forza': random.randint(50, 80),
                'mentalita': random.randint(60, 90),
                'esperienza': random.randint(30, 80)
            }
        elif self.posizione == Position.DIFENSORE:
            return {
                'tecnica': random.randint(40, 75),
                'velocita': random.randint(45, 75),
                'resistenza': random.randint(65, 85),
                'forza': random.randint(70, 90),
                'mentalita': random.randint(60, 85),
                'esperienza': random.randint(40, 80)
            }
        elif self.posizione == Position.CENTROCAMPISTA:
            return {
                'tecnica': random.randint(60, 90),
                'velocita': random.randint(50, 80),
                'resistenza': random.randint(70, 90),
                'forza': random.randint(50, 75),
                'mentalita': random.randint(65, 85),
                'esperienza': random.randint(40, 80)
            }
        else:  # ATTACCANTE
            return {
                'tecnica': random.randint(65, 90),
                'velocita': random.randint(60, 90),
                'resistenza': random.randint(60, 80),
                'forza': random.randint(55, 80),
                'mentalita': random.randint(60, 85),
                'esperienza': random.randint(35, 75)
            }
    
    def _get_age_modifier(self) -> float:
        """Restituisce modificatore basato sull'età"""
        if self.eta < 20:
            return 0.7  # Giovani promettenti ma non ancora maturi
        elif self.eta < 25:
            return random.uniform(0.85, 1.1)  # In crescita
        elif self.eta < 30:
            return random.uniform(0.95, 1.15)  # Nel pieno della carriera
        elif self.eta < 33:
            return random.uniform(0.9, 1.05)  # Ancora buoni ma in declino
        else:
            return random.uniform(0.7, 0.9)  # Veterani
    
    def nome_completo(self) -> str:
        """Restituisce nome e cognome"""
        return f"{self.nome} {self.cognome}"
    
    def is_available(self) -> bool:
        """Verifica se il giocatore è disponibile per giocare"""
        return (self.status == PlayerStatus.DISPONIBILE and 
                self.giorni_infortunio == 0 and 
                self.giornate_squalifica == 0)
    
    def update_weekly(self):
        """Aggiorna lo stato del giocatore settimanalmente"""
        # Riduci giorni di infortunio
        if self.giorni_infortunio > 0:
            self.giorni_infortunio = max(0, self.giorni_infortunio - 7)
            if self.giorni_infortunio == 0:
                self.status = PlayerStatus.DISPONIBILE
        
        # Riduci squalifiche (si riducono solo dopo le partite)
        # Aggiorna forma e morale leggermente
        self.forma = max(50, min(100, self.forma + random.randint(-3, 3)))
        self.morale = max(50, min(100, self.morale + random.randint(-2, 2)))
    
    def get_market_value(self) -> int:
        """Calcola il valore di mercato attuale"""
        base_value = self.stats.overall() * 50000
        
        # Modificatori per età
        if self.eta < 23:
            age_multiplier = 1.2
        elif self.eta < 28:
            age_multiplier = 1.0
        elif self.eta < 32:
            age_multiplier = 0.8
        else:
            age_multiplier = 0.5
        
        # Modificatori per forma e morale
        form_multiplier = (self.forma + self.morale) / 200
        
        return int(base_value * age_multiplier * form_multiplier)
    
    def __str__(self):
        return f"{self.nome_completo()} ({self.posizione.value}) - Overall: {self.stats.overall()}"
