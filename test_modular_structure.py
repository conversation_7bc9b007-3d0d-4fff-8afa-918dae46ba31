#!/usr/bin/env python3
"""
Test della struttura modulare dell'interfaccia
"""

import sys
import os

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_modular_structure():
    """Test della struttura modulare"""
    print("🏗️ Test Struttura Modulare dell'Interfaccia")
    print("=" * 60)
    
    # Test importazioni dei moduli
    print("📦 Test importazioni moduli...")
    
    try:
        from gui.header import HeaderWidget
        print("   ✅ HeaderWidget importato correttamente")
    except ImportError as e:
        print(f"   ❌ Errore importazione HeaderWidget: {e}")
        return False
    
    try:
        from gui.footer import FooterWidget
        print("   ✅ FooterWidget importato correttamente")
    except ImportError as e:
        print(f"   ❌ Errore importazione FooterWidget: {e}")
        return False
    
    try:
        from gui.menu_manager import MenuManager
        print("   ✅ MenuManager importato correttamente")
    except ImportError as e:
        print(f"   ❌ Errore importazione MenuManager: {e}")
        return False
    
    try:
        from gui.finances import FinancesWidget
        print("   ✅ FinancesWidget importato correttamente")
    except ImportError as e:
        print(f"   ❌ Errore importazione FinancesWidget: {e}")
        return False
    
    try:
        from core.game_calendar import game_calendar
        print("   ✅ GameCalendar importato correttamente")
    except ImportError as e:
        print(f"   ❌ Errore importazione GameCalendar: {e}")
        return False
    
    # Test funzionalità del calendario
    print(f"\n📅 Test funzionalità calendario...")
    
    current_date = game_calendar.get_current_date_string()
    print(f"   📊 Data corrente: {current_date}")
    
    phase_info = game_calendar.get_phase_info()
    print(f"   🎯 Fase corrente: {phase_info['fase']}")
    print(f"   📝 Descrizione: {phase_info['descrizione']}")
    
    season_info = game_calendar.get_season_info()
    print(f"   🏆 Stagione: {season_info['stagione']}")
    
    is_transfer_window = game_calendar.is_transfer_window_open()
    print(f"   🔄 Calciomercato aperto: {'Sì' if is_transfer_window else 'No'}")
    
    is_season_active = game_calendar.is_season_active()
    print(f"   ⚽ Stagione attiva: {'Sì' if is_season_active else 'No'}")
    
    # Test avanzamento calendario
    print(f"\n⏭️ Test avanzamento calendario...")
    
    original_date = game_calendar.get_current_date()
    print(f"   📅 Data originale: {original_date.strftime('%d/%m/%Y')}")
    
    # Avanza di 7 giorni
    game_calendar.advance_days(7)
    new_date = game_calendar.get_current_date()
    print(f"   📅 Dopo 7 giorni: {new_date.strftime('%d/%m/%Y')}")
    
    # Ripristina data originale
    game_calendar.current_date = original_date
    print(f"   📅 Data ripristinata: {game_calendar.get_current_date_string()}")
    
    # Test struttura file
    print(f"\n📁 Test struttura file...")
    
    gui_files = [
        "gui/header.py",
        "gui/footer.py", 
        "gui/menu_manager.py",
        "gui/finances.py",
        "gui/main_window.py",
        "gui/dashboard.py",
        "gui/club_selection.py"
    ]
    
    core_files = [
        "core/game_calendar.py",
        "core/player_generator.py"
    ]
    
    for file_path in gui_files + core_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} esiste")
        else:
            print(f"   ❌ {file_path} mancante")
            return False
    
    # Test dimensioni file main_window.py
    print(f"\n📏 Test dimensioni main_window.py...")
    
    with open("gui/main_window.py", "r", encoding="utf-8") as f:
        lines = f.readlines()
        line_count = len(lines)
    
    print(f"   📊 Righe in main_window.py: {line_count}")
    
    if line_count < 300:
        print(f"   ✅ File main_window.py ridotto correttamente ({line_count} righe)")
    else:
        print(f"   ⚠️ File main_window.py ancora grande ({line_count} righe)")
    
    # Test contenuto main_window.py
    print(f"\n🔍 Test contenuto main_window.py...")
    
    with open("gui/main_window.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Verifica che i metodi siano stati rimossi
    removed_methods = [
        "def create_header",
        "def create_footer", 
        "def create_menu_bar",
        "def toggle_game",
        "def advance_week",
        "def advance_to_next_event",
        "def update_calendar_info",
        "def check_calendar_events",
        "def notify_phase_change"
    ]
    
    methods_still_present = []
    for method in removed_methods:
        if method in content:
            methods_still_present.append(method)
    
    if not methods_still_present:
        print(f"   ✅ Tutti i metodi sono stati spostati correttamente")
    else:
        print(f"   ⚠️ Metodi ancora presenti: {', '.join(methods_still_present)}")
    
    # Verifica che le importazioni siano corrette
    required_imports = [
        "from .header import HeaderWidget",
        "from .footer import FooterWidget",
        "from .menu_manager import MenuManager"
    ]
    
    imports_present = []
    for import_line in required_imports:
        if import_line in content:
            imports_present.append(import_line)
    
    print(f"   📦 Importazioni presenti: {len(imports_present)}/{len(required_imports)}")
    for imp in imports_present:
        print(f"      ✅ {imp}")
    
    # Test funzionalità calendario avanzate
    print(f"\n🗓️ Test funzionalità calendario avanzate...")
    
    # Test date importanti
    season_dates = game_calendar.season_dates
    important_dates = [
        "inizio_stagione",
        "apertura_mercato_estivo",
        "chiusura_mercato_estivo", 
        "inizio_serie_a",
        "apertura_mercato_invernale",
        "chiusura_mercato_invernale",
        "fine_serie_a"
    ]
    
    for date_key in important_dates:
        if date_key in season_dates:
            date_value = season_dates[date_key]
            print(f"   📅 {date_key}: {date_value.strftime('%d/%m/%Y')}")
        else:
            print(f"   ❌ Data mancante: {date_key}")
            return False
    
    # Test giorni fino al prossimo evento
    days_until = game_calendar.get_days_until_next_event()
    print(f"   ⏰ Giorni al prossimo evento: {days_until}")
    
    print(f"\n" + "=" * 60)
    print("✅ STRUTTURA MODULARE COMPLETATA CON SUCCESSO!")
    print("=" * 60)
    
    print("🎯 BENEFICI OTTENUTI:")
    print("   • main_window.py ridotto e più leggibile")
    print("   • Componenti separati e riutilizzabili")
    print("   • Header modulare per informazioni club")
    print("   • Footer modulare per controlli gioco")
    print("   • MenuManager per gestione menu")
    print("   • GameCalendar per gestione date")
    print("   • FinancesWidget per gestione finanze")
    print("   • Struttura scalabile per nuove funzionalità")
    
    print(f"\n📊 STATISTICHE:")
    print(f"   • File GUI: {len(gui_files)} file")
    print(f"   • File Core: {len(core_files)} file")
    print(f"   • Righe main_window.py: {line_count}")
    print(f"   • Metodi spostati: {len(removed_methods) - len(methods_still_present)}")
    print(f"   • Importazioni aggiunte: {len(imports_present)}")
    
    return True

if __name__ == "__main__":
    success = test_modular_structure()
    sys.exit(0 if success else 1)
