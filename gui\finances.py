"""
Widget per la gestione delle finanze del club
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QProgressBar, QTableWidget,
                            QTableWidgetItem, QPushButton, QGroupBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

from models.club import Club

class FinanceCard(QFrame):
    """Card per mostrare informazioni finanziarie"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", color: str = "#2196F3", trend: str = ""):
        super().__init__()
        
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedHeight(120)
        self.setStyleSheet(f"""
            QFrame {{
                border-left: 4px solid {color};
                background-color: #f8f9fa;
                border-radius: 5px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        
        # Titolo
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setStyleSheet("color: #666;")
        layout.addWidget(title_label)
        
        # Valore principale
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 18, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # Sottotitolo
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setFont(QFont("Arial", 8))
            subtitle_label.setStyleSheet("color: #888;")
            layout.addWidget(subtitle_label)
        
        # Trend
        if trend:
            trend_label = QLabel(trend)
            trend_label.setFont(QFont("Arial", 9, QFont.Bold))
            trend_color = "#4CAF50" if "+" in trend else "#F44336" if "-" in trend else "#FF9800"
            trend_label.setStyleSheet(f"color: {trend_color};")
            layout.addWidget(trend_label)

class FinancesWidget(QWidget):
    """Widget principale per le finanze"""
    
    def __init__(self, club: Club):
        super().__init__()
        self.club = club
        self.init_ui()
        self.update_data()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        layout = QVBoxLayout(self)
        
        # Sezione panoramica finanziaria
        self.create_overview_section(layout)
        
        # Sezione budget dettagliato
        self.create_budget_section(layout)
        
        # Sezione entrate e uscite
        self.create_income_expenses_section(layout)
        
        # Sezione storico finanziario
        self.create_history_section(layout)
    
    def create_overview_section(self, parent_layout):
        """Crea la sezione panoramica"""
        section_label = QLabel("💰 Panoramica Finanziaria")
        section_label.setFont(QFont("Arial", 14, QFont.Bold))
        parent_layout.addWidget(section_label)
        
        # Grid con le card finanziarie
        cards_layout = QGridLayout()
        parent_layout.addLayout(cards_layout)
        
        # Card Budget Totale
        budget_info = self.club.get_budget_breakdown()
        self.budget_total_card = FinanceCard(
            "Budget Totale",
            f"€{self.club.budget_mln:.1f}M",
            f"€{self.club.budget:,}",
            "#4CAF50"
        )
        cards_layout.addWidget(self.budget_total_card, 0, 0)
        
        # Card Budget Trasferimenti
        self.budget_transfers_card = FinanceCard(
            "Budget Trasferimenti",
            f"€{self.club.budget_trasferimenti / 1000000:.1f}M",
            f"{(self.club.budget_trasferimenti / self.club.budget * 100):.1f}% del totale",
            "#2196F3"
        )
        cards_layout.addWidget(self.budget_transfers_card, 0, 1)
        
        # Card Budget Stipendi
        stipendi_usati = (self.club.uscite_mensili / self.club.budget_stipendi * 100) if self.club.budget_stipendi > 0 else 0
        stipendi_color = "#4CAF50" if stipendi_usati < 80 else "#FF9800" if stipendi_usati < 95 else "#F44336"
        self.budget_salaries_card = FinanceCard(
            "Budget Stipendi",
            f"€{self.club.budget_stipendi / 1000:.0f}K/mese",
            f"{stipendi_usati:.1f}% utilizzato",
            stipendi_color
        )
        cards_layout.addWidget(self.budget_salaries_card, 0, 2)
        
        # Card Bilancio Mensile
        monthly_balance = self.club.get_monthly_balance()
        balance_color = "#4CAF50" if monthly_balance >= 0 else "#F44336"
        balance_trend = f"+€{monthly_balance:,}" if monthly_balance >= 0 else f"€{monthly_balance:,}"
        self.monthly_balance_card = FinanceCard(
            "Bilancio Mensile",
            balance_trend,
            "Entrate - Uscite",
            balance_color
        )
        cards_layout.addWidget(self.monthly_balance_card, 0, 3)
    
    def create_budget_section(self, parent_layout):
        """Crea la sezione budget dettagliato"""
        budget_group = QGroupBox("📊 Distribuzione Budget")
        budget_group.setFont(QFont("Arial", 12, QFont.Bold))
        parent_layout.addWidget(budget_group)
        
        budget_layout = QVBoxLayout(budget_group)
        
        # Progress bar per visualizzare la distribuzione
        budget_info = self.club.get_budget_breakdown()
        
        # Trasferimenti
        transfers_layout = QHBoxLayout()
        transfers_label = QLabel("Trasferimenti:")
        transfers_label.setMinimumWidth(120)
        transfers_layout.addWidget(transfers_label)
        
        transfers_bar = QProgressBar()
        transfers_percentage = (self.club.budget_trasferimenti / self.club.budget * 100)
        transfers_bar.setValue(int(transfers_percentage))
        transfers_bar.setFormat(f"€{self.club.budget_trasferimenti / 1000000:.1f}M ({transfers_percentage:.1f}%)")
        transfers_layout.addWidget(transfers_bar)
        
        budget_layout.addLayout(transfers_layout)
        
        # Stipendi
        salaries_layout = QHBoxLayout()
        salaries_label = QLabel("Stipendi:")
        salaries_label.setMinimumWidth(120)
        salaries_layout.addWidget(salaries_label)
        
        salaries_bar = QProgressBar()
        salaries_annual = self.club.budget_stipendi * 12
        salaries_percentage = (salaries_annual / self.club.budget * 100)
        salaries_bar.setValue(int(salaries_percentage))
        salaries_bar.setFormat(f"€{salaries_annual / 1000000:.1f}M ({salaries_percentage:.1f}%)")
        salaries_layout.addWidget(salaries_bar)
        
        budget_layout.addLayout(salaries_layout)
        
        # Rimanente
        remaining_layout = QHBoxLayout()
        remaining_label = QLabel("Altri costi:")
        remaining_label.setMinimumWidth(120)
        remaining_layout.addWidget(remaining_label)
        
        remaining_bar = QProgressBar()
        remaining_amount = budget_info['budget_rimanente']
        remaining_percentage = (remaining_amount / self.club.budget * 100)
        remaining_bar.setValue(int(remaining_percentage))
        remaining_bar.setFormat(f"€{remaining_amount / 1000000:.1f}M ({remaining_percentage:.1f}%)")
        remaining_layout.addWidget(remaining_bar)
        
        budget_layout.addLayout(remaining_layout)
    
    def create_income_expenses_section(self, parent_layout):
        """Crea la sezione entrate e uscite"""
        income_expenses_group = QGroupBox("💸 Entrate e Uscite Mensili")
        income_expenses_group.setFont(QFont("Arial", 12, QFont.Bold))
        parent_layout.addWidget(income_expenses_group)
        
        ie_layout = QHBoxLayout(income_expenses_group)
        
        # Tabella entrate
        income_layout = QVBoxLayout()
        income_label = QLabel("📈 Entrate")
        income_label.setFont(QFont("Arial", 11, QFont.Bold))
        income_label.setStyleSheet("color: #4CAF50;")
        income_layout.addWidget(income_label)
        
        self.income_table = QTableWidget(5, 2)
        self.income_table.setHorizontalHeaderLabels(["Fonte", "Importo"])
        self.income_table.setMaximumHeight(200)
        income_layout.addWidget(self.income_table)
        
        ie_layout.addLayout(income_layout)
        
        # Tabella uscite
        expenses_layout = QVBoxLayout()
        expenses_label = QLabel("📉 Uscite")
        expenses_label.setFont(QFont("Arial", 11, QFont.Bold))
        expenses_label.setStyleSheet("color: #F44336;")
        expenses_layout.addWidget(expenses_label)
        
        self.expenses_table = QTableWidget(5, 2)
        self.expenses_table.setHorizontalHeaderLabels(["Voce", "Importo"])
        self.expenses_table.setMaximumHeight(200)
        expenses_layout.addWidget(self.expenses_table)
        
        ie_layout.addLayout(expenses_layout)
    
    def create_history_section(self, parent_layout):
        """Crea la sezione storico"""
        history_group = QGroupBox("📋 Storico Finanziario")
        history_group.setFont(QFont("Arial", 12, QFont.Bold))
        parent_layout.addWidget(history_group)
        
        history_layout = QVBoxLayout(history_group)
        
        # Placeholder per lo storico
        history_label = QLabel("Storico finanziario non ancora disponibile")
        history_label.setAlignment(Qt.AlignCenter)
        history_label.setStyleSheet("color: #666; font-style: italic;")
        history_layout.addWidget(history_label)
    
    def update_data(self):
        """Aggiorna tutti i dati finanziari"""
        self.update_overview_cards()
        self.update_income_expenses_tables()
    
    def update_overview_cards(self):
        """Aggiorna le card della panoramica"""
        # Aggiorna budget totale
        budget_labels = self.budget_total_card.findChildren(QLabel)
        budget_labels[1].setText(f"€{self.club.budget_mln:.1f}M")
        
        # Aggiorna budget trasferimenti
        transfer_labels = self.budget_transfers_card.findChildren(QLabel)
        transfer_labels[1].setText(f"€{self.club.budget_trasferimenti / 1000000:.1f}M")
        
        # Aggiorna budget stipendi
        stipendi_usati = (self.club.uscite_mensili / self.club.budget_stipendi * 100) if self.club.budget_stipendi > 0 else 0
        salary_labels = self.budget_salaries_card.findChildren(QLabel)
        salary_labels[1].setText(f"€{self.club.budget_stipendi / 1000:.0f}K/mese")
        
        # Aggiorna bilancio mensile
        monthly_balance = self.club.get_monthly_balance()
        balance_labels = self.monthly_balance_card.findChildren(QLabel)
        balance_trend = f"+€{monthly_balance:,}" if monthly_balance >= 0 else f"€{monthly_balance:,}"
        balance_labels[1].setText(balance_trend)
    
    def update_income_expenses_tables(self):
        """Aggiorna le tabelle di entrate e uscite"""
        # Entrate
        income_data = [
            ("Ricavi stadio", f"€{self.club.entrate_mensili:,}"),
            ("Sponsorizzazioni", "€0"),
            ("Diritti TV", "€0"),
            ("Merchandising", "€0"),
            ("Altri ricavi", "€0")
        ]
        
        for i, (source, amount) in enumerate(income_data):
            self.income_table.setItem(i, 0, QTableWidgetItem(source))
            self.income_table.setItem(i, 1, QTableWidgetItem(amount))
        
        # Uscite
        expenses_data = [
            ("Stipendi giocatori", f"€{self.club.uscite_mensili:,}"),
            ("Stipendi staff", "€0"),
            ("Manutenzione stadio", "€0"),
            ("Spese operative", "€0"),
            ("Altri costi", "€0")
        ]
        
        for i, (expense, amount) in enumerate(expenses_data):
            self.expenses_table.setItem(i, 0, QTableWidgetItem(expense))
            self.expenses_table.setItem(i, 1, QTableWidgetItem(amount))

    def get_financial_projections(self):
        """Calcola le proiezioni finanziarie"""
        monthly_balance = self.club.get_monthly_balance()

        projections = {
            "3_mesi": monthly_balance * 3,
            "6_mesi": monthly_balance * 6,
            "12_mesi": monthly_balance * 12,
            "fine_stagione": monthly_balance * 10  # Circa 10 mesi di stagione
        }

        return projections

    def get_budget_health_status(self):
        """Valuta lo stato di salute del budget"""
        monthly_balance = self.club.get_monthly_balance()
        budget_usage = (self.club.uscite_mensili / self.club.entrate_mensili * 100) if self.club.entrate_mensili > 0 else 100

        if monthly_balance >= 0 and budget_usage < 80:
            return "Eccellente", "#4CAF50"
        elif monthly_balance >= 0 and budget_usage < 95:
            return "Buono", "#8BC34A"
        elif monthly_balance >= 0:
            return "Accettabile", "#FF9800"
        elif monthly_balance > -50000:
            return "Preoccupante", "#FF5722"
        else:
            return "Critico", "#F44336"
