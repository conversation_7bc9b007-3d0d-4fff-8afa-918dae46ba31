"""
Widget per la gestione della rosa
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QTableWidget, QTableWidgetItem,
                            QPushButton, QGroupBox, QComboBox, QLineEdit,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor

from models.club import Club
from models.player import Position

class PlayerCard(QFrame):
    """Card per mostrare informazioni di un giocatore"""
    
    player_selected = pyqtSignal(object)  # Segnale quando un giocatore viene selezionato
    
    def __init__(self, player):
        super().__init__()
        self.player = player
        self.init_ui()
    
    def init_ui(self):
        """Inizializza l'interfaccia della card"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setFixedHeight(100)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
                margin: 2px;
            }
            QFrame:hover {
                background-color: #e3f2fd;
                border-color: #2196F3;
            }
        """)
        
        layout = QHBoxLayout(self)
        
        # Informazioni principali
        info_layout = QVBoxLayout()
        
        # Nome e posizione
        name_label = QLabel(f"{self.player.nome_completo()}")
        name_label.setFont(QFont("Arial", 12, QFont.Bold))
        info_layout.addWidget(name_label)
        
        # Dettagli
        details = f"{self.player.posizione.value} • {self.player.eta} anni • {self.player.nazionalita}"
        details_label = QLabel(details)
        details_label.setFont(QFont("Arial", 9))
        details_label.setStyleSheet("color: #666;")
        info_layout.addWidget(details_label)
        
        # Valore e stipendio
        financial = f"Valore: €{self.player.valore_mercato:,} • Stipendio: €{self.player.stipendio:,}/mese"
        financial_label = QLabel(financial)
        financial_label.setFont(QFont("Arial", 8))
        financial_label.setStyleSheet("color: #888;")
        info_layout.addWidget(financial_label)
        
        layout.addLayout(info_layout)
        
        # Overall
        overall_layout = QVBoxLayout()
        overall_label = QLabel("Overall")
        overall_label.setFont(QFont("Arial", 8))
        overall_label.setAlignment(Qt.AlignCenter)
        overall_layout.addWidget(overall_label)
        
        overall_value = QLabel(str(self.player.stats.overall()))
        overall_value.setFont(QFont("Arial", 16, QFont.Bold))
        overall_value.setAlignment(Qt.AlignCenter)
        overall_value.setStyleSheet("color: #2196F3;")
        overall_layout.addWidget(overall_value)
        
        layout.addLayout(overall_layout)
    
    def mousePressEvent(self, event):
        """Gestisce il click sulla card"""
        if event.button() == Qt.LeftButton:
            self.player_selected.emit(self.player)

class SquadWidget(QWidget):
    """Widget principale per la gestione della rosa"""
    
    def __init__(self, club: Club):
        super().__init__()
        self.club = club
        self.current_filter = "Tutti"
        self.current_search = ""
        self.init_ui()
        self.update_data()
    
    def init_ui(self):
        """Inizializza l'interfaccia utente"""
        layout = QVBoxLayout(self)
        
        # Sezione filtri e ricerca
        self.create_filters_section(layout)
        
        # Sezione statistiche rosa
        self.create_stats_section(layout)
        
        # Sezione lista giocatori
        self.create_players_section(layout)
        
        # Sezione dettagli giocatore selezionato
        self.create_player_details_section(layout)
    
    def create_filters_section(self, parent_layout):
        """Crea la sezione filtri e ricerca"""
        filters_frame = QFrame()
        filters_frame.setFrameStyle(QFrame.StyledPanel)
        filters_frame.setMaximumHeight(60)
        parent_layout.addWidget(filters_frame)
        
        filters_layout = QHBoxLayout(filters_frame)
        
        # Filtro per posizione
        position_label = QLabel("Posizione:")
        filters_layout.addWidget(position_label)
        
        self.position_combo = QComboBox()
        self.position_combo.addItems(["Tutti", "Portieri", "Difensori", "Centrocampisti", "Attaccanti"])
        self.position_combo.currentTextChanged.connect(self.on_filter_changed)
        filters_layout.addWidget(self.position_combo)
        
        filters_layout.addStretch()
        
        # Ricerca
        search_label = QLabel("Cerca:")
        filters_layout.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Nome giocatore...")
        self.search_edit.textChanged.connect(self.on_search_changed)
        filters_layout.addWidget(self.search_edit)
        
        # Pulsanti azioni
        self.add_player_btn = QPushButton("Aggiungi Giocatore")
        self.add_player_btn.clicked.connect(self.add_player)
        filters_layout.addWidget(self.add_player_btn)
    
    def create_stats_section(self, parent_layout):
        """Crea la sezione statistiche della rosa"""
        stats_group = QGroupBox("📊 Statistiche Rosa")
        stats_group.setFont(QFont("Arial", 12, QFont.Bold))
        parent_layout.addWidget(stats_group)
        
        self.stats_labels = {} # Dizionario per tenere traccia delle etichette delle statistiche
        stats_layout = QGridLayout(stats_group)
        
        positions_data = [
            (Position.PORTIERE, "Portieri", "#FF5722"),
            (Position.DIFENSORE, "Difensori", "#2196F3"),
            (Position.CENTROCAMPISTA, "Centrocampisti", "#4CAF50"),
            (Position.ATTACCANTE, "Attaccanti", "#FF9800")
        ]
        
        for i, (position, name, color) in enumerate(positions_data):
            pos_frame = QFrame()
            pos_frame.setFrameStyle(QFrame.StyledPanel)
            pos_frame.setStyleSheet(f"border-left: 4px solid {color}; background-color: #f8f9fa;")
            
            pos_layout = QVBoxLayout(pos_frame)
            
            pos_name = QLabel(name)
            pos_name.setFont(QFont("Arial", 11, QFont.Bold))
            pos_name.setStyleSheet(f"color: {color};")
            pos_layout.addWidget(pos_name)
            
            stats_label = QLabel("") # Etichetta vuota, verrà aggiornata in update_stats
            stats_label.setFont(QFont("Arial", 8))
            pos_layout.addWidget(stats_label)
            
            self.stats_labels[position] = stats_label # Salva il riferimento all'etichetta
            
            stats_layout.addWidget(pos_frame, 0, i)
    
    def create_players_section(self, parent_layout):
        """Crea la sezione lista giocatori"""
        players_group = QGroupBox("👥 Rosa")
        players_group.setFont(QFont("Arial", 12, QFont.Bold))
        parent_layout.addWidget(players_group)
        
        players_layout = QVBoxLayout(players_group)
        
        # Tabella giocatori
        self.players_table = QTableWidget()
        self.players_table.setColumnCount(8)
        self.players_table.setHorizontalHeaderLabels([
            "Nome", "Posizione", "Età", "Nazionalità", "Overall", 
            "Valore", "Stipendio", "Azioni"
        ])
        
        # Configura la tabella
        header = self.players_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Nome
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Posizione
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Età
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Nazionalità
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Overall
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Valore
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Stipendio
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Azioni
        
        self.players_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.players_table.setAlternatingRowColors(True)
        self.players_table.itemSelectionChanged.connect(self.on_player_selection_changed)

        players_layout.addWidget(self.players_table)
    
    def create_player_details_section(self, parent_layout):
        """Crea la sezione dettagli giocatore"""
        details_group = QGroupBox("🔍 Dettagli Giocatore")
        details_group.setFont(QFont("Arial", 12, QFont.Bold))
        details_group.setMaximumHeight(200)
        parent_layout.addWidget(details_group)
        
        details_layout = QVBoxLayout(details_group)
        
        self.player_details_label = QLabel("Seleziona un giocatore per vedere i dettagli")
        self.player_details_label.setAlignment(Qt.AlignCenter)
        self.player_details_label.setStyleSheet("color: #666; font-style: italic;")
        details_layout.addWidget(self.player_details_label)
    
    def update_data(self):
        """Aggiorna tutti i dati della rosa"""
        self.update_players_table()
        self.update_stats()
    
    def update_players_table(self):
        """Aggiorna la tabella dei giocatori"""
        # Filtra i giocatori
        filtered_players = self.get_filtered_players()
        
        self.players_table.setRowCount(len(filtered_players))
        
        for row, player in enumerate(filtered_players):
            # Nome
            name_item = QTableWidgetItem(player.nome_completo())
            name_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.players_table.setItem(row, 0, name_item)
            
            # Posizione
            pos_item = QTableWidgetItem(player.posizione.value)
            self.players_table.setItem(row, 1, pos_item)
            
            # Età
            age_item = QTableWidgetItem(str(player.eta))
            self.players_table.setItem(row, 2, age_item)
            
            # Nazionalità
            nat_item = QTableWidgetItem(player.nazionalita)
            self.players_table.setItem(row, 3, nat_item)
            
            # Overall
            overall_item = QTableWidgetItem(str(player.stats.overall()))
            overall_item.setFont(QFont("Arial", 10, QFont.Bold))
            overall_item.setForeground(QColor("#2196F3"))
            self.players_table.setItem(row, 4, overall_item)
            
            # Valore
            value_item = QTableWidgetItem(f"€{player.valore_mercato:,}")
            self.players_table.setItem(row, 5, value_item)
            
            # Stipendio
            salary_item = QTableWidgetItem(f"€{player.stipendio:,}")
            self.players_table.setItem(row, 6, salary_item)
            
            # Azioni (placeholder)
            actions_item = QTableWidgetItem("Dettagli")
            self.players_table.setItem(row, 7, actions_item)
    
    def update_stats(self):
        """Aggiorna le statistiche della rosa"""
        positions_data = [
            (Position.PORTIERE, "Portieri", "#FF5722"),
            (Position.DIFENSORE, "Difensori", "#2196F3"),
            (Position.CENTROCAMPISTA, "Centrocampisti", "#4CAF50"),
            (Position.ATTACCANTE, "Attaccanti", "#FF9800")
        ]

        for position, name, color in positions_data:
            players = self.club.get_players_by_position(position)
            count = len(players)
            avg_overall = int(sum(p.stats.overall() for p in players) / count) if count > 0 else 0
            total_value = sum(p.valore_mercato for p in players)
            total_salary = sum(p.stipendio for p in players)
            
            stats_text = f"Giocatori: {count}\nOverall medio: {avg_overall}\nValore totale: €{total_value:,}\nStipendi: €{total_salary:,}/mese"
            
            if position in self.stats_labels:
                self.stats_labels[position].setText(stats_text)
    
    def get_filtered_players(self):
        """Restituisce i giocatori filtrati"""
        players = self.club.giocatori
        
        # Filtro per posizione
        if self.current_filter != "Tutti":
            position_map = {
                "Portieri": Position.PORTIERE,
                "Difensori": Position.DIFENSORE,
                "Centrocampisti": Position.CENTROCAMPISTA,
                "Attaccanti": Position.ATTACCANTE
            }
            if self.current_filter in position_map:
                players = [p for p in players if p.posizione == position_map[self.current_filter]]
        
        # Filtro per ricerca
        if self.current_search:
            search_lower = self.current_search.lower()
            players = [p for p in players if search_lower in p.nome_completo().lower()]
        
        return players
    
    def on_filter_changed(self, filter_text):
        """Gestisce il cambio di filtro"""
        self.current_filter = filter_text
        self.update_players_table()
    
    def on_search_changed(self, search_text):
        """Gestisce il cambio di ricerca"""
        self.current_search = search_text
        self.update_players_table()
    
    def add_player(self):
        """Apre la finestra per aggiungere un giocatore"""
        # Placeholder per la funzionalità di aggiunta giocatore
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "Aggiungi Giocatore", "Funzionalità non ancora implementata")
    
    def on_player_selection_changed(self):
        """Gestisce il cambio di selezione nella tabella giocatori"""
        selected_items = self.players_table.selectedItems()
        if not selected_items:
            self.player_details_label.setText("Seleziona un giocatore per vedere i dettagli")
            self.player_details_label.setAlignment(Qt.AlignCenter)
            return

        # Ottieni la riga selezionata
        row = selected_items[0].row()
        filtered_players = self.get_filtered_players()

        if row < len(filtered_players):
            player = filtered_players[row]
            self.show_player_details(player)

    def show_player_details(self, player):
        """Mostra i dettagli di un giocatore"""
        details_text = f"""
<b>{player.nome_completo()}</b><br>
<b>Posizione:</b> {player.posizione.value}<br>
<b>Età:</b> {player.eta} anni<br>
<b>Nazionalità:</b> {player.nazionalita}<br>
<b>Overall:</b> {player.stats.overall()}<br>
<b>Valore di mercato:</b> €{player.valore_mercato:,}<br>
<b>Stipendio:</b> €{player.stipendio:,}/mese<br><br>
<b>Statistiche:</b><br>
Attacco: {player.stats.attacco}<br>
Difesa: {player.stats.difesa}<br>
Fisico: {player.stats.fisico}<br>
Velocità: {player.stats.velocita}<br>
Tecnica: {player.stats.tecnica}
"""
        self.player_details_label.setText(details_text)
        self.player_details_label.setAlignment(Qt.AlignLeft)
